<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realistic Multi-Round Follow-On Model</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .model {
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .current {
            background-color: #fff5f5;
            border-color: #f56565;
        }
        .realistic {
            background-color: #f0fff4;
            border-color: #48bb78;
        }
        .example {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: monospace;
        }
        .highlight {
            background-color: #fef5e7;
            padding: 2px 4px;
            border-radius: 3px;
        }
        button {
            background-color: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background-color: #4338ca;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Realistic Multi-Round Follow-On Model</h1>
        <p>Comparing the current "all-at-once" follow-on approach vs. a more realistic multi-round strategy</p>

        <div class="comparison">
            <div class="model current">
                <h3>❌ Current Model (Unrealistic)</h3>
                <div class="example">
Initial Seed: $2M
Series A Follow-on: $4M (2.0x all at once)
Total Investment: $6M

Follow-on Decision: Single binary choice
Risk: High concentration risk
                </div>
                <p><strong>Problems:</strong></p>
                <ul>
                    <li>Too aggressive - 2x investment in one round</li>
                    <li>No optionality for future rounds</li>
                    <li>Unrealistic VC behavior</li>
                    <li>High capital commitment risk</li>
                </ul>
            </div>

            <div class="model realistic">
                <h3>✅ Realistic Multi-Round Model</h3>
                <div class="example">
Initial Seed: $2M
Series A Follow-on: $1M (0.5x)
Series B Follow-on: $1.5M (0.75x)
Series C Follow-on: $1M (0.5x)
Total Follow-on: $3.5M (1.75x total)

Each Follow-on: Independent decision
Risk: Graduated exposure
                </div>
                <p><strong>Benefits:</strong></p>
                <ul>
                    <li>Smaller, manageable follow-on sizes</li>
                    <li>Multiple decision points</li>
                    <li>Realistic VC behavior</li>
                    <li>Better risk management</li>
                </ul>
            </div>
        </div>

        <h2>🎯 Proposed Follow-On Parameters</h2>
        <div class="example">
<strong>Per-Round Follow-On Multipliers (of original check):</strong>
Series A follow-on: 0.3x - 0.7x (avg 0.5x)
Series B follow-on: 0.5x - 1.2x (avg 0.8x)  
Series C follow-on: 0.3x - 0.8x (avg 0.5x)
Growth follow-on: 0.2x - 0.5x (avg 0.3x)

<strong>Maximum Total Follow-On per Company:</strong>
Seed → All stages: Max 2.5x of original
Series A → Later stages: Max 2.0x of original
Series B → Later stages: Max 1.5x of original
Series C → Growth: Max 1.0x of original

<strong>Follow-On Probability per Round (based on performance):</strong>
Company performing >3x: 80% chance per round
Company performing 2-3x: 60% chance per round
Company performing 1.5-2x: 40% chance per round
Company performing 1-1.5x: 20% chance per round
Company performing <1x: 5% chance per round
        </div>

        <button onclick="runComparison()">🔬 Run Monte Carlo Comparison</button>
        <button onclick="showDetailedExample()">📊 Show Detailed Example</button>

        <div id="results" class="results" style="display:none;">
            <!-- Results will be populated here -->
        </div>
    </div>

    <script>
        // Realistic multi-round follow-on parameters
        const REALISTIC_FOLLOW_ON = {
            // Per-round follow-on sizes (as multiple of original check)
            roundMultipliers: {
                "Seed_SeriesA": { min: 0.3, max: 0.7, avg: 0.5 },
                "Seed_SeriesB": { min: 0.5, max: 1.2, avg: 0.8 },
                "Seed_SeriesC": { min: 0.3, max: 0.8, avg: 0.5 },
                "Seed_Growth": { min: 0.2, max: 0.5, avg: 0.3 },
                "SeriesA_SeriesB": { min: 0.4, max: 0.9, avg: 0.6 },
                "SeriesA_SeriesC": { min: 0.6, max: 1.2, avg: 0.9 },
                "SeriesA_Growth": { min: 0.3, max: 0.7, avg: 0.5 },
                "SeriesB_SeriesC": { min: 0.5, max: 1.0, avg: 0.7 },
                "SeriesB_Growth": { min: 0.4, max: 0.8, avg: 0.6 },
                "SeriesC_Growth": { min: 0.3, max: 1.0, avg: 0.6 }
            },
            
            // Maximum total follow-on across all rounds
            maxTotalFollowOn: {
                "Seed": 2.5,      // Max 2.5x of original across all rounds
                "SeriesA": 2.0,   // Max 2.0x of original across remaining rounds
                "SeriesB": 1.5,   // Max 1.5x of original across remaining rounds
                "SeriesC": 1.0,   // Max 1.0x of original for growth stage
                "Growth": 0.5     // Limited late-stage follow-on
            },
            
            // Follow-on probability per round based on current performance
            getFollowOnProbability: (currentMultiple, totalFollowOnSoFar, maxTotal, originalCheck) => {
                // Reduce probability if we're approaching max follow-on
                const followOnRatio = totalFollowOnSoFar / originalCheck;
                const capacityFactor = Math.max(0, (maxTotal - followOnRatio) / maxTotal);
                
                let baseProb = 0;
                if (currentMultiple >= 3.0) baseProb = 0.8;
                else if (currentMultiple >= 2.0) baseProb = 0.6;
                else if (currentMultiple >= 1.5) baseProb = 0.4;
                else if (currentMultiple >= 1.0) baseProb = 0.2;
                else baseProb = 0.05;
                
                return baseProb * capacityFactor;
            }
        };

        // Current model parameters (for comparison)
        const CURRENT_FOLLOW_ON = {
            followOnMultiple: {
                "Seed": 2.0,
                "SeriesA": 1.6,
                "SeriesB": 1.4,
                "SeriesC": 1.2,
                "Growth": 0.8
            },
            getFollowOnProbability: (currentMultiple) => {
                if (currentMultiple >= 3.0) return 0.9;
                if (currentMultiple >= 2.0) return 0.7;
                if (currentMultiple >= 1.5) return 0.4;
                if (currentMultiple >= 1.0) return 0.2;
                return 0.05;
            }
        };

        function runComparison() {
            const results = document.getElementById('results');
            results.style.display = 'block';
            results.innerHTML = '<h3>🔬 Monte Carlo Comparison Results</h3><p>Running 1,000 simulations...</p>';
            
            // Simulate both models
            const currentResults = simulateModel('current', 1000);
            const realisticResults = simulateModel('realistic', 1000);
            
            results.innerHTML = `
                <h3>📊 Comparison Results (1,000 simulations each)</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>❌ Current Model</h4>
                        <p><strong>Avg Follow-on per Company:</strong> $${(currentResults.avgFollowOn / 1e6).toFixed(1)}M</p>
                        <p><strong>Avg Total Investment:</strong> $${(currentResults.avgTotalInvestment / 1e6).toFixed(1)}M</p>
                        <p><strong>Follow-on Frequency:</strong> ${(currentResults.followOnFreq * 100).toFixed(1)}%</p>
                        <p><strong>Portfolio Concentration Risk:</strong> ${currentResults.concentrationRisk.toFixed(2)}</p>
                    </div>
                    <div>
                        <h4>✅ Realistic Model</h4>
                        <p><strong>Avg Follow-on per Company:</strong> $${(realisticResults.avgFollowOn / 1e6).toFixed(1)}M</p>
                        <p><strong>Avg Total Investment:</strong> $${(realisticResults.avgTotalInvestment / 1e6).toFixed(1)}M</p>
                        <p><strong>Follow-on Frequency:</strong> ${(realisticResults.followOnFreq * 100).toFixed(1)}%</p>
                        <p><strong>Portfolio Concentration Risk:</strong> ${realisticResults.concentrationRisk.toFixed(2)}</p>
                    </div>
                </div>
                <div style="margin-top: 20px; padding: 15px; background-color: #e8f5e8; border-radius: 6px;">
                    <h4>💡 Key Insights</h4>
                    <ul>
                        <li><strong>More Realistic Follow-on Sizes:</strong> Realistic model averages ${((realisticResults.avgFollowOn / currentResults.avgFollowOn - 1) * 100).toFixed(0)}% ${realisticResults.avgFollowOn < currentResults.avgFollowOn ? 'lower' : 'higher'} follow-on per company</li>
                        <li><strong>Better Risk Management:</strong> ${(((currentResults.concentrationRisk - realisticResults.concentrationRisk) / currentResults.concentrationRisk) * 100).toFixed(0)}% reduction in concentration risk</li>
                        <li><strong>More Opportunities:</strong> Multiple decision points vs. single all-or-nothing choice</li>
                    </ul>
                </div>
            `;
        }

        function simulateModel(modelType, numTrials) {
            let totalFollowOn = 0;
            let totalInvestment = 0;
            let followOnCount = 0;
            let totalCompanies = 0;
            let concentrationRisk = 0;

            for (let trial = 0; trial < numTrials; trial++) {
                // Simulate a seed investment
                const initialCheck = 2e6; // $2M seed
                let currentMultiple = 1.5 + Math.random() * 2; // Random performance 1.5x - 3.5x
                let totalFollowOnInvestment = 0;

                if (modelType === 'current') {
                    // Current model: single follow-on decision
                    const prob = CURRENT_FOLLOW_ON.getFollowOnProbability(currentMultiple);
                    if (Math.random() < prob) {
                        const followOnAmount = initialCheck * CURRENT_FOLLOW_ON.followOnMultiple["Seed"];
                        totalFollowOnInvestment = followOnAmount;
                        followOnCount++;
                    }
                } else {
                    // Realistic model: multiple follow-on opportunities
                    const stages = ["SeriesA", "SeriesB", "SeriesC", "Growth"];
                    const maxTotal = REALISTIC_FOLLOW_ON.maxTotalFollowOn["Seed"];
                    
                    for (const stage of stages) {
                        const key = `Seed_${stage}`;
                        if (REALISTIC_FOLLOW_ON.roundMultipliers[key]) {
                            const prob = REALISTIC_FOLLOW_ON.getFollowOnProbability(
                                currentMultiple, 
                                totalFollowOnInvestment, 
                                maxTotal * initialCheck, 
                                initialCheck
                            );
                            
                            if (Math.random() < prob) {
                                const multiplier = REALISTIC_FOLLOW_ON.roundMultipliers[key];
                                const followOnAmount = initialCheck * (multiplier.min + Math.random() * (multiplier.max - multiplier.min));
                                
                                if (totalFollowOnInvestment + followOnAmount <= maxTotal * initialCheck) {
                                    totalFollowOnInvestment += followOnAmount;
                                    followOnCount++;
                                }
                            }
                            
                            // Company performance evolves over time
                            currentMultiple *= (0.9 + Math.random() * 0.4); // Some randomness in performance
                        }
                    }
                }

                totalFollowOn += totalFollowOnInvestment;
                totalInvestment += initialCheck + totalFollowOnInvestment;
                totalCompanies++;
                
                // Calculate concentration risk (how much capital is in follow-ons vs. new investments)
                if (totalFollowOnInvestment > 0) {
                    concentrationRisk += (totalFollowOnInvestment / (initialCheck + totalFollowOnInvestment));
                }
            }

            return {
                avgFollowOn: totalFollowOn / totalCompanies,
                avgTotalInvestment: totalInvestment / totalCompanies,
                followOnFreq: followOnCount / totalCompanies,
                concentrationRisk: concentrationRisk / totalCompanies
            };
        }

        function showDetailedExample() {
            const results = document.getElementById('results');
            results.style.display = 'block';
            results.innerHTML = `
                <h3>📊 Detailed Example: $2M Seed Investment Journey</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div style="background-color: #fff5f5; padding: 20px; border-radius: 8px;">
                        <h4>❌ Current Model</h4>
                        <div style="font-family: monospace; line-height: 1.8;">
                            <strong>Year 0:</strong> $2M Seed investment<br/>
                            <strong>Year 2:</strong> Company at 2.5x (70% follow-on probability)<br/>
                            <strong>Decision:</strong> Make $4M Series A follow-on (2.0x)<br/>
                            <br/>
                            <strong>Final Investment:</strong> $6M total<br/>
                            <strong>Risk:</strong> <span class="highlight">High concentration in one decision</span><br/>
                            <strong>Flexibility:</strong> <span class="highlight">None after Series A</span>
                        </div>
                    </div>
                    
                    <div style="background-color: #f0fff4; padding: 20px; border-radius: 8px;">
                        <h4>✅ Realistic Multi-Round Model</h4>
                        <div style="font-family: monospace; line-height: 1.8;">
                            <strong>Year 0:</strong> $2M Seed investment<br/>
                            <strong>Year 2:</strong> Company at 2.5x → $1M Series A follow-on (0.5x)<br/>
                            <strong>Year 4:</strong> Company at 4.2x → $1.6M Series B follow-on (0.8x)<br/>
                            <strong>Year 6:</strong> Company at 6.8x → $1M Series C follow-on (0.5x)<br/>
                            <br/>
                            <strong>Final Investment:</strong> $5.6M total<br/>
                            <strong>Risk:</strong> <span class="highlight">Graduated, manageable exposure</span><br/>
                            <strong>Flexibility:</strong> <span class="highlight">Multiple decision points</span>
                        </div>
                    </div>
                </div>
                
                <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
                    <h4>🎯 Why This Matters</h4>
                    <ul>
                        <li><strong>Capital Efficiency:</strong> Smaller initial follow-ons allow testing before bigger commitments</li>
                        <li><strong>Risk Management:</strong> Can stop follow-ons if company performance deteriorates</li>
                        <li><strong>Portfolio Balance:</strong> Avoid over-concentration in single follow-on decisions</li>
                        <li><strong>Realistic Behavior:</strong> Matches how VCs actually make follow-on decisions</li>
                        <li><strong>Optionality:</strong> Multiple chances to increase or decrease conviction</li>
                    </ul>
                </div>
            `;
        }
    </script>
</body>
</html> 