<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dry Powder Optimizer - Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 40px;
            font-size: 1.1em;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }

        .input-group {
            margin-bottom: 25px;
        }

        .slider-group {
            margin-bottom: 30px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        input[type="number"], input[type="range"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="range"] {
            padding: 0;
            cursor: pointer;
        }

        input[type="number"]:focus {
            outline: none;
            border-color: #3498db;
        }

        .slider-value {
            display: inline-block;
            min-width: 60px;
            text-align: right;
            font-weight: 600;
            color: #3498db;
        }

        .allocation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stage-allocation {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .stage-allocation strong {
            cursor: help;
            border-bottom: 1px dashed #7f8c8d;
        }

        .stage-allocation input[type="range"] {
            margin-top: 10px;
        }

        .deal-count {
            font-size: 1.2em;
            font-weight: 700;
            color: #3498db;
            margin-top: 5px;
        }

        button {
            padding: 14px 24px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }

        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }

        .results {
            display: none;
        }

        .results.show {
            display: block;
        }

        .scenario-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .metric-value {
            font-size: 2em;
            font-weight: 700;
            color: #3498db;
            margin: 10px 0;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.9em;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin-top: 20px;
        }

        .allocation-summary {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #3498db;
        }

        .deal-summary {
            background: #f0f8ff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #3498db;
        }

        input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            cursor: pointer;
        }

        .checkbox-group {
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .allocation-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Dry Powder Optimizer</h1>
        <p class="subtitle">Find optimal follow-on reserve percentage for your fund and target allocation</p>
        <p class="subtitle" style="font-size: 0.9em; margin-top: -30px; color: #95a5a6;">
            Input your fund size and stage preferences, see projected deal counts in real-time
        </p>

        <div class="card">
            <h2>Fund Configuration</h2>
            
            <div class="input-group">
                <label for="fundSize">
                    Fund Size (millions)
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Total committed capital for your fund</span>
                    </span>
                </label>
                <input type="number" id="fundSize" value="300" min="50" max="5000" step="50">
            </div>

            <h3 style="margin-top: 30px; margin-bottom: 15px;">Deal Allocation by Stage</h3>
            <p style="color: #7f8c8d; font-size: 0.9em; margin-bottom: 20px;">
                Adjust deal counts by stage. Each slider increments by 1 deal.
            </p>
            <div class="allocation-grid">
                <div class="stage-allocation">
                    <strong title="Average check: $3M (Range: $1M-$5M)">Seed</strong>
                    <div class="deal-count" id="seedDeals">12 deals</div>
                    <input type="range" id="seedCount" min="0" max="50" value="12" step="1" class="stage-slider">
                    <div style="color: #7f8c8d; font-size: 0.9em;"><span id="seedPercent">0</span>% of capital</div>
                </div>
                <div class="stage-allocation">
                    <strong title="Average check: $11M (Range: $7M-$15M)">Series A</strong>
                    <div class="deal-count" id="seriesADeals">8 deals</div>
                    <input type="range" id="seriesACount" min="0" max="25" value="8" step="1" class="stage-slider">
                    <div style="color: #7f8c8d; font-size: 0.9em;"><span id="seriesAPercent">0</span>% of capital</div>
                </div>
                <div class="stage-allocation">
                    <strong title="Average check: $17.5M (Range: $10M-$25M)">Series B</strong>
                    <div class="deal-count" id="seriesBDeals">5 deals</div>
                    <input type="range" id="seriesBCount" min="0" max="20" value="5" step="1" class="stage-slider">
                    <div style="color: #7f8c8d; font-size: 0.9em;"><span id="seriesBPercent">0</span>% of capital</div>
                </div>
                <div class="stage-allocation">
                    <strong title="Average check: $27.5M (Range: $15M-$40M)">Series C</strong>
                    <div class="deal-count" id="seriesCDeals">3 deals</div>
                    <input type="range" id="seriesCCount" min="0" max="15" value="3" step="1" class="stage-slider">
                    <div style="color: #7f8c8d; font-size: 0.9em;"><span id="seriesCPercent">0</span>% of capital</div>
                </div>
                <div class="stage-allocation">
                    <strong title="Average check: $40M (Range: $30M-$50M)">Growth</strong>
                    <div class="deal-count" id="growthDeals">1 deal</div>
                    <input type="range" id="growthCount" min="0" max="10" value="1" step="1" class="stage-slider">
                    <div style="color: #7f8c8d; font-size: 0.9em;"><span id="growthPercent">0</span>% of capital</div>
                </div>
            </div>

            <div class="allocation-summary" id="allocationSummary">
                Total allocation: <strong id="totalAllocation">0</strong>%
                <span id="allocationWarning" style="color: #e74c3c; display: none; margin-left: 10px;">
                    ⚠️ Must equal 100%
                </span>
            </div>

            <div class="deal-summary">
                <h3 style="margin: 0 0 10px 0;">Portfolio Summary</h3>
                <p style="margin: 0;">
                    Initial capital available: $<span id="initialCapital">180</span>M
                    <span style="color: #7f8c8d;">(assuming 40% follow-on reserve)</span>
                </p>
                <p style="margin: 10px 0 0 0; font-size: 1.2em;">
                    <strong>Total deals: <span id="totalDeals" style="color: #3498db;">29</span></strong>
                </p>
            </div>
        </div>

        <div class="card">
            <h2>Optimization Parameters</h2>
            
            <div class="input-group checkbox-group">
                <label>
                    <input type="checkbox" id="enableRecycling" checked>
                    Enable Capital Recycling
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Reinvest returns from early exits (up to 105% of fund)</span>
                    </span>
                </label>
            </div>

            <div class="input-group">
                <label for="targetNetTVPI">
                    Target Net TVPI
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Minimum acceptable net return multiple after fees</span>
                    </span>
                </label>
                <input type="number" id="targetNetTVPI" value="2.0" min="1.5" max="5" step="0.1">
            </div>

            <div class="input-group">
                <label for="managementFee">
                    Management Fee (% annually)
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Annual management fee as % of committed capital</span>
                    </span>
                </label>
                <input type="number" id="managementFee" value="2" min="1" max="3" step="0.25">
            </div>

            <div class="input-group">
                <label for="numTrials">
                    Monte Carlo Trials
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Number of simulations to run per scenario</span>
                    </span>
                </label>
                <input type="number" id="numTrials" value="5000" min="1000" max="25000" step="1000">
            </div>

            <button onclick="runDryPowderOptimization()">
                Find Optimal Follow-On Reserve %
            </button>
        </div>

        <div class="results" id="results">
            <div class="card" style="background: #f0f8ff; border: 1px solid #3498db; margin-bottom: 20px;">
                <p style="margin: 0; color: #2c3e50;">
                    <strong>📊 Analysis:</strong> Testing different follow-on reserve percentages to minimize dry powder while achieving target returns.
                </p>
            </div>
            
            <div id="scenariosContainer"></div>
            
            <div class="card">
                <h3>Follow-On Reserve vs. Performance</h3>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dry powder optimization engine
        
        // Check sizes with proper ranges from original model
        const CHECK_SIZES = {
            "Seed": { min: 1_000_000, max: 5_000_000, avg: 3_000_000 },
            "SeriesA": { min: 7_000_000, max: 15_000_000, avg: 11_000_000 },
            "SeriesB": { min: 10_000_000, max: 25_000_000, avg: 17_500_000 },
            "SeriesC": { min: 15_000_000, max: 40_000_000, avg: 27_500_000 },
            "Growth": { min: 30_000_000, max: 50_000_000, avg: 40_000_000 }
        };

        // Enhanced concentration monitoring
        const CONCENTRATION_LIMITS = {
            singleCompany: 0.15,  // 15% max per company
            singleSector: 0.40,   // 40% max per sector
            followOnCap: 0.05     // Additional 5% cap for follow-ons
        };

        function updateAllocation() {
            const fundSizeInput = document.getElementById('fundSize');
            const fundSizeM = parseFloat(fundSizeInput.value) || 300;
            const fundSize = fundSizeM * 1_000_000;
            
            // Assume 40% follow-on reserve for display purposes
            const followOnReservePercent = 40;
            const followOnReserve = fundSize * followOnReservePercent / 100;
            const initialCapital = fundSize - followOnReserve;
            document.getElementById('initialCapital').textContent = Math.round(initialCapital / 1e6);
            
            const stages = [
                { name: 'seed', dbName: 'Seed' },
                { name: 'seriesA', dbName: 'SeriesA' },
                { name: 'seriesB', dbName: 'SeriesB' },
                { name: 'seriesC', dbName: 'SeriesC' },
                { name: 'growth', dbName: 'Growth' }
            ];
            
            let totalCapitalNeeded = 0;
            let totalDeals = 0;
            
            stages.forEach(stage => {
                const countSlider = document.getElementById(stage.name + 'Count');
                const dealsDiv = document.getElementById(stage.name + 'Deals');
                const percentSpan = document.getElementById(stage.name + 'Percent');
                
                if (countSlider && dealsDiv && percentSpan) {
                    const dealCount = parseInt(countSlider.value);
                    dealsDiv.textContent = dealCount + (dealCount === 1 ? ' deal' : ' deals');
                    
                    // Calculate capital needed for these deals
                    const avgCheckSize = CHECK_SIZES[stage.dbName].avg;
                    const capitalForStage = dealCount * avgCheckSize;
                    totalCapitalNeeded += capitalForStage;
                    totalDeals += dealCount;
                    
                    // Calculate and display percentage of initial capital
                    const percentOfCapital = initialCapital > 0 ? (capitalForStage / initialCapital * 100) : 0;
                    percentSpan.textContent = percentOfCapital.toFixed(1);
                }
            });
            
            // Calculate total percentage
            const totalPercent = initialCapital > 0 ? (totalCapitalNeeded / initialCapital * 100) : 0;
            document.getElementById('totalAllocation').textContent = Math.round(totalPercent);
            document.getElementById('totalDeals').textContent = totalDeals;
            
            const warning = document.getElementById('allocationWarning');
            // Show warning if allocation is significantly off 
            if (totalPercent > 110) {
                warning.style.display = 'inline';
                warning.innerHTML = '⚠️ Over-allocated - deals may be constrained by available capital';
            } else if (totalPercent < 70) {
                warning.style.display = 'inline';
                warning.innerHTML = '⚠️ Low allocation - consider adding more deals';
            } else {
                warning.style.display = 'none';
            }
        }

        // Wait for DOM
        document.addEventListener('DOMContentLoaded', function() {
            // Fund size input
            document.getElementById('fundSize').addEventListener('input', updateAllocation);
            
            // Deal count sliders - use the new IDs
            ['seedCount', 'seriesACount', 'seriesBCount', 'seriesCCount', 'growthCount'].forEach(id => {
                const slider = document.getElementById(id);
                if (slider) {
                    slider.addEventListener('input', updateAllocation);
                }
            });
            
            // Initial update
            updateAllocation();
        });

        // Add the stage parameters for return distributions
        const STAGE_PARAMS = {
            "Seed":    [0.65, 0.70, 1.00, 2.3, 10.0, 0.15], // [p0, mu, sigma, alpha, xmin, tail_weight]
            "SeriesA": [0.35, 0.90, 0.80, 2.5, 10.0, 0.10],
            "SeriesB": [0.20, 1.10, 0.60, 2.8, 10.0, 0.06],
            "SeriesC": [0.10, 1.00, 0.60, 2.5, 8.0, 0.08],
            "Growth":  [0.05, 0.95, 0.45, 2.8, 7.0, 0.05],
        };

        const FOLLOW_ON_PARAMS = {
            // Updated follow-on probability based on 2022-2024 market data
            probFollowOn: (currentMultiple, stage) => {
                if (currentMultiple === 0) return 0.05;  // Zombies rarely get follow-on
                
                // Base graduation rates by stage (from research data)
                const baseGraduationRates = {
                    "Seed": 0.20,      // 15-20% Seed-to-A per recent data
                    "SeriesA": 0.55,   // 50-60% A-to-B estimated
                    "SeriesB": 0.60,   // ~60% B-to-C
                    "SeriesC": 0.65,   // Higher for proven companies
                    "Growth": 0.70     // Mature companies
                };
                
                const baseRate = baseGraduationRates[stage] || 0.50;
                
                // Performance multipliers
                let performanceMultiplier;
                
                if (currentMultiple < 0.5) {
                    performanceMultiplier = 0.3;  // Severe underperformers
                } else if (currentMultiple < 1.0) {
                    performanceMultiplier = 0.6;  // Below water
                } else if (currentMultiple < 1.5) {
                    performanceMultiplier = 0.9;  // Modest performance
                } else if (currentMultiple < 2.0) {
                    performanceMultiplier = 1.2;  // Good performance
                } else if (currentMultiple < 3.0) {
                    performanceMultiplier = 1.5;  // Strong performance
                } else {
                    // Exceptional performers - but stage matters
                    if (stage === "Seed" || stage === "SeriesA") {
                        performanceMultiplier = 1.8;  // Early winners still attractive
                    } else {
                        performanceMultiplier = 1.6;  // Later stage faces valuation concerns
                    }
                }
                
                // Calculate probability
                let probability = baseRate * performanceMultiplier;
                
                // Stage-specific caps reflecting market reality
                if (stage === "Seed") {
                    probability = Math.min(0.40, probability);  // Even great Seed faces Series A crunch
                } else if (stage === "Growth") {
                    probability = Math.min(0.85, probability);  // Late stage has limits
                } else {
                    probability = Math.min(0.95, probability);  // General cap
                }
                
                return probability;
            },
            followOnMultiple: {
                "Seed": 2.0,
                "SeriesA": 1.6,
                "SeriesB": 1.4,
                "SeriesC": 1.2,
                "Growth": 0.8
            },
            nextStage: {
                "Seed": "SeriesA",
                "SeriesA": "SeriesB",
                "SeriesB": "SeriesC",
                "SeriesC": "Growth",
                "Growth": "Growth"
            }
        };

        const RECYCLING_PARAMS = {
            recyclingProbability: {
                "Seed": 0.03,
                "SeriesA": 0.05,
                "SeriesB": 0.08,
                "SeriesC": 0.12,
                "Growth": 0.15
            },
            recyclingMultiple: {
                "Seed": 3.0,
                "SeriesA": 2.5,
                "SeriesB": 2.2,
                "SeriesC": 2.0,
                "Growth": 1.8
            },
            maxRecyclingPercent: 100  // Keep at 100% to prevent over-deployment
        };

        // Statistical functions
        function getRandomCheckSize(stage) {
            const range = CHECK_SIZES[stage];
            return range.min + Math.random() * (range.max - range.min);
        }

        function normalCDF(x, mean, stdDev) {
            const z = (x - mean) / stdDev;
            const t = 1 / (1 + 0.2316419 * Math.abs(z));
            const d = 0.3989423 * Math.exp(-z * z / 2);
            const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
            return z > 0 ? 1 - prob : prob;
        }

        function normalPPF(p, mean, stdDev) {
            if (p <= 0) return -Infinity;
            if (p >= 1) return Infinity;
            
            const a1 = -3.969683028665376e+01;
            const a2 = 2.209460984245205e+02;
            const a3 = -2.759285104469687e+02;
            const a4 = 1.383577518672690e+02;
            const a5 = -3.066479806614716e+01;
            const a6 = 2.506628277459239e+00;
            
            const b1 = -5.447609879822406e+01;
            const b2 = 1.615858368580409e+02;
            const b3 = -1.556989798598866e+02;
            const b4 = 6.680131188771972e+01;
            const b5 = -1.328068155288572e+01;
            
            const c1 = -7.784894002430293e-03;
            const c2 = -3.223964580411365e-01;
            const c3 = -2.400758277161838e+00;
            const c4 = -2.549732539343734e+00;
            const c5 = 4.374664141464968e+00;
            const c6 = 2.938163982698783e+00;
            
            const d1 = 7.784695709041462e-03;
            const d2 = 3.224671290700398e-01;
            const d3 = 2.445134137142996e+00;
            const d4 = 3.754408661907416e+00;
            
            let q, r;
            
            if (p < 0.02425) {
                q = Math.sqrt(-2 * Math.log(p));
                return mean + stdDev * (((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / 
                       ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
            } else if (p < 0.97575) {
                q = p - 0.5;
                r = q * q;
                return mean + stdDev * (((((a1 * r + a2) * r + a3) * r + a4) * r + a5) * r + a6) * q / 
                       (((((b1 * r + b2) * r + b3) * r + b4) * r + b5) * r + 1);
            } else {
                q = Math.sqrt(-2 * Math.log(1 - p));
                return mean + stdDev * -(((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / 
                       ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
            }
        }

        function drawMultiplesVectorized(stage, numDraws) {
            if (numDraws === 0) return [];
            
            const [p0, mu, sigma, alpha, xmin, tailWeight] = STAGE_PARAMS[stage];
            const results = new Array(numDraws).fill(0);
            
            for (let i = 0; i < numDraws; i++) {
                const randP0 = Math.random();
                
                if (randP0 < p0) {
                    results[i] = 0;
                } else {
                    const randTail = Math.random();
                    
                    if (randTail < tailWeight) {
                        const u = Math.random();
                        results[i] = xmin * Math.pow(1 - u, -1 / alpha);
                    } else {
                        const logXmin = Math.log(xmin);
                        const FLogXmin = normalCDF(logXmin, mu, sigma);
                        const u = Math.random();
                        const targetCDF = u * FLogXmin;
                        const logMultiple = normalPPF(targetCDF, mu, sigma);
                        results[i] = Math.min(Math.exp(logMultiple), xmin * (1 - 1e-10));
                    }
                }
            }
            
            return results;
        }

        // Main simulation function
        function simulatePortfolio(mix, fundSize, nTrials, enableFollowOn = true, followOnReservePercent = 40, mgmtFeeAnnual = 0.02, carry = 0.20, fundLife = 10, enableRecycling = true) {
            let estimatedCapital = 0;
            let totalInvestments = 0;
            const investmentPlan = [];
            
            for (const [stage, numDeals] of Object.entries(mix)) {
                if (numDeals > 0) {
                    estimatedCapital += numDeals * CHECK_SIZES[stage].avg;
                    totalInvestments += numDeals;
                    investmentPlan.push({ stage, numDeals });
                }
            }
            
            if (totalInvestments === 0) {
                return {
                    meanTVPI: 0,
                    meanNetTVPI: 0,
                    avgUndeployedCapital: fundSize,
                    numInvestments: 0
                };
            }
            
            const trialTVPIs = new Array(nTrials);
            const trialNetTVPIs = new Array(nTrials);
            const trialCapitalInvested = new Array(nTrials);
            const followOnReserve = enableFollowOn ? (fundSize * followOnReservePercent / 100) : 0;
            const totalMgmtFees = fundSize * mgmtFeeAnnual * fundLife;
            
            // MATHEMATICAL FIX: Work backwards from deployment target
            // Fix total deployment target and vary allocation between initial vs follow-on
            const TARGET_DEPLOYMENT_RATE = 0.88; // Deploy 88% of fund target
            const targetTotalDeployment = fundSize * TARGET_DEPLOYMENT_RATE;
            
            // Estimate follow-on usage (typically 60-80% of reserve gets used)
            const expectedFollowOnUtilization = 0.70;
            const expectedFollowOnDeployment = followOnReserve * expectedFollowOnUtilization;
            
            // Estimate recycling (typically 3-6% of fund)
            const expectedRecyclingRate = 0.04;
            const expectedRecycling = fundSize * expectedRecyclingRate;
            
            // Calculate initial capital budget to hit target deployment
            const initialCapitalBudget = targetTotalDeployment - expectedFollowOnDeployment - expectedRecycling;
            
            // Scale portfolio to fit initial capital budget
            const portfolioScalingFactor = Math.max(0.3, Math.min(1.0, initialCapitalBudget / estimatedCapital));
            
            let totalFollowOnCapital = 0;
            let totalRecycledCapital = 0;
            
            for (let trial = 0; trial < nTrials; trial++) {
                let totalValue = 0;
                let trialFollowOnCapital = 0;
                let trialInitialCapital = 0;
                let trialRecycled = 0;
                let remainingFollowOnReserve = followOnReserve;
                
                const companies = [];
                
                // Create scaled portfolio based on mathematical budget
                for (const plan of investmentPlan) {
                    const scaledDeals = Math.max(1, Math.round(plan.numDeals * portfolioScalingFactor));
                    for (let i = 0; i < scaledDeals; i++) {
                        const checkSize = getRandomCheckSize(plan.stage);
                        
                        companies.push({
                            stage: plan.stage,
                            checkSize: checkSize,
                            isRecycled: false
                        });
                        trialInitialCapital += checkSize;
                    }
                }
                
                // Handle recycling
                if (enableRecycling) {
                    for (const company of companies) {
                        if (Math.random() < RECYCLING_PARAMS.recyclingProbability[company.stage]) {
                            const recyclingMultiple = RECYCLING_PARAMS.recyclingMultiple[company.stage];
                            const returnedCapital = company.checkSize * recyclingMultiple;
                            
                            const maxRecyclable = fundSize * RECYCLING_PARAMS.maxRecyclingPercent / 100;
                            const totalDeployedSoFar = trialInitialCapital + trialFollowOnCapital + trialRecycled;
                            
                            if (totalDeployedSoFar < maxRecyclable) {
                                const recycleAmount = Math.min(company.checkSize, maxRecyclable - totalDeployedSoFar);
                                trialRecycled += recycleAmount;
                                company.isRecycled = true;
                                totalValue += returnedCapital;
                            }
                        }
                    }
                }
                
                // Process non-recycled companies
                for (const company of companies) {
                    if (!company.isRecycled) {
                        const initialMultiple = drawMultiplesVectorized(company.stage, 1)[0];
                        
                        // Follow-on logic
                        if (enableFollowOn && remainingFollowOnReserve > 0) {
                            const probOfFollowOn = FOLLOW_ON_PARAMS.probFollowOn(initialMultiple, company.stage);
                            
                            if (Math.random() < probOfFollowOn) {
                                const followOnSize = company.checkSize * FOLLOW_ON_PARAMS.followOnMultiple[company.stage];
                                
                                if (followOnSize <= remainingFollowOnReserve) {
                                    remainingFollowOnReserve -= followOnSize;
                                    trialFollowOnCapital += followOnSize;
                                    
                                    const followOnStage = FOLLOW_ON_PARAMS.nextStage[company.stage];
                                    const followOnMultiple = drawMultiplesVectorized(followOnStage, 1)[0];
                                    totalValue += followOnSize * followOnMultiple;
                                }
                            }
                        }
                        
                        totalValue += company.checkSize * initialMultiple;
                    }
                }
                
                totalFollowOnCapital += trialFollowOnCapital;
                totalRecycledCapital += trialRecycled;
                trialCapitalInvested[trial] = trialInitialCapital;
                
                trialTVPIs[trial] = totalValue / fundSize;
                
                // Net calculation
                const grossReturn = totalValue;
                const profits = Math.max(0, grossReturn - fundSize);
                const carriedInterest = profits * carry;
                const netReturn = grossReturn - totalMgmtFees - carriedInterest;
                trialNetTVPIs[trial] = netReturn / fundSize;
            }
            
            // Calculate statistics
            const meanTVPI = trialTVPIs.reduce((sum, x) => sum + x, 0) / nTrials;
            const meanNetTVPI = trialNetTVPIs.reduce((sum, x) => sum + x, 0) / nTrials;
            
            const probNetGt2x = trialNetTVPIs.filter(x => x >= 2).length / nTrials;
            const probNetGt3x = trialNetTVPIs.filter(x => x >= 3).length / nTrials;
            
            // Calculate deployed capital
            const avgCapitalInvested = trialCapitalInvested.reduce((sum, x) => sum + x, 0) / nTrials;
            const avgFollowOnCapital = totalFollowOnCapital / nTrials;
            const avgRecycled = totalRecycledCapital / nTrials;
            const totalDeployed = avgCapitalInvested + avgFollowOnCapital + avgRecycled;
            const avgUndeployedCapital = Math.max(0, fundSize - totalDeployed);
            
            // Enhanced debug for mathematical validation
            if (followOnReservePercent === 20 || followOnReservePercent === 40 || followOnReservePercent === 60) {
                console.log(`\n=== ${followOnReservePercent}% Follow-On Reserve Mathematical Analysis ===`);
                console.log(`Fund size: $${(fundSize/1e6).toFixed(1)}M`);
                console.log(`Follow-on reserve: $${(followOnReserve/1e6).toFixed(1)}M`);
                console.log(`Target deployment: $${(targetTotalDeployment/1e6).toFixed(1)}M (${(TARGET_DEPLOYMENT_RATE*100).toFixed(0)}%)`);
                console.log(`Initial budget: $${(initialCapitalBudget/1e6).toFixed(1)}M`);
                console.log(`Portfolio scaling: ${(portfolioScalingFactor*100).toFixed(1)}%`);
                console.log(`--- Actual Results ---`);
                console.log(`Avg initial invested: $${(avgCapitalInvested/1e6).toFixed(1)}M`);
                console.log(`Avg follow-on used: $${(avgFollowOnCapital/1e6).toFixed(1)}M (${((avgFollowOnCapital/followOnReserve)*100).toFixed(1)}% utilization)`);
                console.log(`Avg recycled: $${(avgRecycled/1e6).toFixed(1)}M`);
                console.log(`Total deployed: $${(totalDeployed/1e6).toFixed(1)}M (${((totalDeployed/fundSize)*100).toFixed(1)}%)`);
                console.log(`Undeployed capital: $${(avgUndeployedCapital/1e6).toFixed(1)}M (${((avgUndeployedCapital/fundSize)*100).toFixed(1)}%)`);
            }
            
            return {
                meanTVPI,
                meanNetTVPI,
                probNetGt2x,
                probNetGt3x,
                avgUndeployedCapital,
                numInvestments: Math.round(totalInvestments * portfolioScalingFactor),
                recyclingStats: enableRecycling ? {
                    avgRecycledCapital: avgRecycled,
                    recyclingAsPercentOfFund: (avgRecycled / fundSize) * 100
                } : null
            };
        }

        function getMixFromAllocations(fundSize, followOnReservePercent) {
            const stages = [
                { name: 'seed', dbName: 'Seed' },
                { name: 'seriesA', dbName: 'SeriesA' },
                { name: 'seriesB', dbName: 'SeriesB' },
                { name: 'seriesC', dbName: 'SeriesC' },
                { name: 'growth', dbName: 'Growth' }
            ];
            
            const mix = {};
            
            // Simply get the deal counts from the sliders - no scaling
            stages.forEach(stage => {
                const countSlider = document.getElementById(stage.name + 'Count');
                if (countSlider) {
                    const numDeals = parseInt(countSlider.value);
                    if (numDeals > 0) {
                        mix[stage.dbName] = numDeals;
                    }
                }
            });
            
            return mix;
        }

        // Main optimization function
        let performanceChart = null;
        
        async function runDryPowderOptimization() {
            // Validate allocation is reasonable
            const totalAllocation = parseInt(document.getElementById('totalAllocation').textContent);
            if (totalAllocation < 80 || totalAllocation > 120) {
                alert('Please adjust allocations to be closer to 100% (between 80-120%)');
                return;
            }
            
            // Get parameters
            const fundSizeM = parseFloat(document.getElementById('fundSize').value);
            const fundSize = fundSizeM * 1_000_000;
            const enableRecycling = document.getElementById('enableRecycling').checked;
            const targetNetTVPI = parseFloat(document.getElementById('targetNetTVPI').value);
            const mgmtFee = parseFloat(document.getElementById('managementFee').value) / 100;
            const numTrials = parseInt(document.getElementById('numTrials').value);
            
            // Show results section
            document.getElementById('results').classList.add('show');
            const container = document.getElementById('scenariosContainer');
            container.innerHTML = '<p style="text-align: center; color: #7f8c8d;">Running simulations...</p>';
            
            // Test follow-on reserve percentages from 20% to 60%
            const scenarios = [];
            
            // Use setTimeout to allow UI to update
            setTimeout(() => {
                for (let followOnReservePercent = 20; followOnReservePercent <= 60; followOnReservePercent += 5) {
                    const mix = getMixFromAllocations(fundSize, followOnReservePercent);
                    
                    const stats = simulatePortfolio(
                        mix, 
                        fundSize, 
                        numTrials, 
                        true, 
                        followOnReservePercent,
                        mgmtFee,
                        0.20, // carry
                        10,   // fund life
                        enableRecycling
                    );
                    
                    const dryPowderPercent = (stats.avgUndeployedCapital / fundSize) * 100;
                    const meetsTarget = stats.meanNetTVPI >= targetNetTVPI;
                    
                    scenarios.push({
                        followOnReservePercent,
                        stats,
                        dryPowderPercent,
                        meetsTarget
                    });
                }
                
                // Debug: log all scenarios
                console.log('Optimization Results:');
                scenarios.forEach(s => {
                    console.log(`${s.followOnReservePercent}% reserve -> ${s.dryPowderPercent.toFixed(1)}% dry powder, Net TVPI: ${s.stats.meanNetTVPI.toFixed(2)}x${s.meetsTarget ? ' ✓' : ''}`);
                });
                
                // Enhanced validation checks
                console.log('\n=== VALIDATION CHECKS ===');
                
                // Sort scenarios by follow-on reserve percentage to ensure proper order
                scenarios.sort((a, b) => a.followOnReservePercent - b.followOnReservePercent);
                
                // Check monotonicity of dry powder
                let isMonotonic = true;
                for (let i = 1; i < scenarios.length; i++) {
                    if (scenarios[i].dryPowderPercent < scenarios[i-1].dryPowderPercent) {
                        isMonotonic = false;
                        console.log(`❌ Non-monotonic: ${scenarios[i-1].followOnReservePercent}% has ${scenarios[i-1].dryPowderPercent.toFixed(1)}% dry powder, ${scenarios[i].followOnReservePercent}% has ${scenarios[i].dryPowderPercent.toFixed(1)}%`);
                    }
                }
                
                if (isMonotonic) {
                    console.log('✅ PASS: Dry powder increases monotonically with follow-on reserve %');
                } else {
                    console.log('❌ FAIL: Dry powder is not monotonic - there may be logic errors');
                }
                
                const lowReserveScenario = scenarios[0]; // 20%
                const highReserveScenario = scenarios[scenarios.length - 1]; // 60%
                
                console.log(`${lowReserveScenario.followOnReservePercent}% reserve dry powder: ${lowReserveScenario.dryPowderPercent.toFixed(1)}%`);
                console.log(`${highReserveScenario.followOnReservePercent}% reserve dry powder: ${highReserveScenario.dryPowderPercent.toFixed(1)}%`);
                
                const dryPowderRange = highReserveScenario.dryPowderPercent - lowReserveScenario.dryPowderPercent;
                console.log(`Dry powder range: ${dryPowderRange.toFixed(1)} percentage points`);
                
                // Check that dry powder percentages are reasonable 
                const avgDryPowder = scenarios.reduce((sum, s) => sum + s.dryPowderPercent, 0) / scenarios.length;
                console.log(`Average dry powder across scenarios: ${avgDryPowder.toFixed(1)}%`);
                
                if (avgDryPowder > 1.0 && dryPowderRange > 5.0) {
                    console.log('✅ PASS: Dry powder calculations appear realistic with good range');
                } else {
                    console.log('❌ FAIL: Dry powder too low or insufficient range - may indicate calculation error');
                }
                
                // Find optimal scenario
                const viableScenarios = scenarios.filter(s => s.meetsTarget);
                console.log(`Found ${viableScenarios.length} scenarios that meet target ${targetNetTVPI}x`);
                
                // Among scenarios that meet the target, find the one with LOWEST dry powder
                const optimalScenario = viableScenarios.length > 0 
                    ? viableScenarios.reduce((a, b) => a.dryPowderPercent < b.dryPowderPercent ? a : b)
                    : scenarios.reduce((a, b) => a.stats.meanNetTVPI > b.stats.meanNetTVPI ? a : b);
                
                console.log(`Optimal: ${optimalScenario.followOnReservePercent}% reserve with ${optimalScenario.dryPowderPercent.toFixed(1)}% dry powder`);
                
                displayResults(scenarios, optimalScenario, targetNetTVPI);
            }, 100);
        }

        function displayResults(scenarios, optimal, targetNetTVPI) {
            const container = document.getElementById('scenariosContainer');
            container.innerHTML = '';
            
            // Display top scenarios
            const topScenarios = scenarios
                .sort((a, b) => {
                    if (a.meetsTarget !== b.meetsTarget) return b.meetsTarget - a.meetsTarget;
                    return a.dryPowderPercent - b.dryPowderPercent;
                })
                .slice(0, 5);
            
            topScenarios.forEach(scenario => {
                const card = document.createElement('div');
                card.className = 'scenario-card';
                if (scenario === optimal) {
                    card.style.borderLeft = '4px solid #27ae60';
                }
                
                const isOptimal = scenario === optimal;
                
                const fundSizeM = parseFloat(document.getElementById('fundSize').value);
                const initialCapital = fundSizeM * (100 - scenario.followOnReservePercent) / 100;
                
                card.innerHTML = `
                    <h3>${isOptimal ? '✅ Optimal: ' : ''}${scenario.followOnReservePercent}% Follow-On Reserve</h3>
                    <p style="margin: 10px 0; color: #7f8c8d;">
                        Initial capital: $${initialCapital.toFixed(0)}M | Follow-on reserve: $${(fundSizeM - initialCapital).toFixed(0)}M
                    </p>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-label">Mean Net TVPI</div>
                            <div class="metric-value" style="color: ${scenario.meetsTarget ? '#27ae60' : '#e74c3c'};">
                                ${scenario.stats.meanNetTVPI.toFixed(2)}x
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Dry Powder</div>
                            <div class="metric-value" style="color: ${scenario.dryPowderPercent < 10 ? '#27ae60' : '#e74c3c'};">
                                ${scenario.dryPowderPercent.toFixed(1)}%
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">P(Net ≥ ${targetNetTVPI}x)</div>
                            <div class="metric-value">
                                ${((targetNetTVPI <= 2 ? scenario.stats.probNetGt2x : scenario.stats.probNetGt3x) * 100).toFixed(1)}%
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Avg Undeployed</div>
                            <div class="metric-value">
                                $${(scenario.stats.avgUndeployedCapital / 1e6).toFixed(1)}M
                            </div>
                        </div>
                    </div>
                    ${scenario.stats.recyclingStats ? `
                        <p style="margin-top: 15px; color: #27ae60;">
                            ♻️ Recycling: $${(scenario.stats.recyclingStats.avgRecycledCapital / 1e6).toFixed(1)}M 
                            (${scenario.stats.recyclingStats.recyclingAsPercentOfFund.toFixed(1)}% of fund)
                        </p>
                    ` : ''}
                `;
                
                container.appendChild(card);
            });
            
            // Create chart
            createChart(scenarios, targetNetTVPI);
        }

        function createChart(scenarios, targetNetTVPI) {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            if (performanceChart) {
                performanceChart.destroy();
            }
            
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: scenarios.map(s => s.followOnReservePercent + '%'),
                    datasets: [
                        {
                            label: 'Mean Net TVPI',
                            data: scenarios.map(s => ({
                                x: s.followOnReservePercent,
                                y: s.stats.meanNetTVPI
                            })),
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            yAxisID: 'y-tvpi',
                            tension: 0.1
                        },
                        {
                            label: 'Dry Powder %',
                            data: scenarios.map(s => ({
                                x: s.followOnReservePercent,
                                y: s.dryPowderPercent
                            })),
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            yAxisID: 'y-dry',
                            tension: 0.1
                        },
                        {
                            label: `Target (${targetNetTVPI}x)`,
                            data: scenarios.map(s => ({
                                x: s.followOnReservePercent,
                                y: targetNetTVPI
                            })),
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            pointRadius: 0,
                            yAxisID: 'y-tvpi'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Follow-On Reserve Analysis'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            display: true,
                            title: {
                                display: true,
                                text: 'Follow-On Reserve %'
                            },
                            min: 20,
                            max: 60,
                            ticks: {
                                stepSize: 5,
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        'y-tvpi': {
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Net TVPI'
                            }
                        },
                        'y-dry': {
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Dry Powder %'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 