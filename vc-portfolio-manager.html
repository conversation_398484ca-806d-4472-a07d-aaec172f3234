<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VC Portfolio Manager - Complete Investment Tracking</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .company-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .company-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .company-selector select, .company-selector input, .company-selector button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .company-selector button {
            background-color: #4f46e5;
            color: white;
            border: none;
            cursor: pointer;
        }
        .timeline-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .timeline {
            position: relative;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 50px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #4f46e5;
        }
        .timeline-item {
            position: relative;
            padding: 20px 20px 20px 80px;
            margin-bottom: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 44px;
            top: 25px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #4f46e5;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #4f46e5;
        }
        .investment-item::before {
            background: #10b981;
            box-shadow: 0 0 0 3px #10b981;
        }
        .quarter-header {
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 10px;
            font-size: 18px;
        }
        .investment-header {
            font-weight: bold;
            color: #10b981;
            margin-bottom: 10px;
            font-size: 18px;
        }
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        .data-item {
            display: flex;
            flex-direction: column;
        }
        .data-item label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .data-item input, .data-item select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .data-item input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        .add-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .add-buttons button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
        }
        .add-investment {
            background-color: #10b981;
            color: white;
        }
        .add-quarter {
            background-color: #4f46e5;
            color: white;
        }
        .remove-item {
            background-color: #ef4444;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4f46e5;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .performance-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .performing-well {
            background-color: #dcfce7;
            color: #166534;
        }
        .performing-ok {
            background-color: #fef3c7;
            color: #92400e;
        }
        .performing-poor {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .export-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f1f5f9;
            border-radius: 8px;
        }
        .export-section button {
            background-color: #0ea5e9;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
        }
        .scenario-card {
            transition: all 0.3s ease;
        }
        .scenario-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .scenario-card.selected {
            background: #e8f0fe !important;
            border-color: #1a73e8 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 VC Portfolio Manager</h1>
        <p>Complete investment tracking with multiple follow-ons and quarterly marks</p>

        <div class="company-selector">
            <select id="companySelect" onchange="selectCompany()">
                <option value="">Select Company...</option>
            </select>
            <input type="text" id="newCompanyName" placeholder="New company name">
            <button onclick="addNewCompany()">➕ Add Company</button>
            <button onclick="savePortfolio()">💾 Save Portfolio</button>
            <button onclick="loadSampleData()">📋 Load Sample Data</button>
        </div>

        <div id="companyView" style="display:none;">
            <div class="company-header">
                <div>
                    <h2 id="companyTitle">Company Name</h2>
                    <p id="companySummary">Investment summary will appear here</p>
                </div>
                <div style="text-align: right;">
                    <div class="stat-value" id="totalInvested">$0M</div>
                    <div class="stat-label">Total Invested</div>
                </div>
            </div>

            <div class="add-buttons">
                <button class="add-investment" onclick="addInvestment()">💰 Add Investment Round</button>
                <button class="add-quarter" onclick="addQuarterlyMark()">📈 Add Quarterly Mark</button>
            </div>

            <div class="timeline-container">
                <div id="timeline" class="timeline">
                    <!-- Timeline items will be added here -->
                </div>
            </div>

            <div class="summary-stats" id="summaryStats">
                <!-- Summary statistics will be populated here -->
            </div>
        </div>

        <div class="export-section">
            <h3>📤 Export & Analysis</h3>
            <button onclick="exportToCSV()">📊 Export to CSV</button>
            <button onclick="generateReport()">📋 Generate Report</button>
            <button onclick="showPortfolioSummary()">📈 Portfolio Summary</button>
            <button onclick="showForecasting()">🚀 Monte Carlo Forecasting</button>
        </div>

        <!-- Monte Carlo Forecasting Section -->
        <div id="forecastingSection" style="display:none; margin-top: 30px;">
            <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h2>🚀 Portfolio Forecasting & Deployment Strategy Analysis</h2>
                
                <!-- Fund Setup -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">Fund Size ($M)</label>
                        <input type="number" id="fundSize" value="400" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">Fund Vintage</label>
                        <input type="number" id="fundVintage" value="2021" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">Management Fee (%)</label>
                        <input type="number" id="managementFee" value="2" step="0.1" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">Carried Interest (%)</label>
                        <input type="number" id="carriedInterest" value="20" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                </div>

                <!-- Current Performance Dashboard -->
                <div id="currentPerformance" style="margin: 20px 0;">
                    <h3>📊 Current Portfolio Performance</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 15px 0;">
                        <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #4f46e5;" id="currentGrossTVPI">-</div>
                            <div style="font-size: 12px; color: #666;">Gross TVPI</div>
                        </div>
                        <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #4f46e5;" id="currentNetTVPI">-</div>
                            <div style="font-size: 12px; color: #666;">Net TVPI</div>
                        </div>
                        <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #4f46e5;" id="currentIRR">-</div>
                            <div style="font-size: 12px; color: #666;">Current IRR</div>
                        </div>
                        <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #4f46e5;" id="remainingCapital">-</div>
                            <div style="font-size: 12px; color: #666;">Dry Powder</div>
                        </div>
                        <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #4f46e5;" id="deploymentRate">-</div>
                            <div style="font-size: 12px; color: #666;">Deployed</div>
                        </div>
                    </div>
                </div>

                <!-- Historical Performance Charts -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 30px 0;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h4>TVPI Over Time</h4>
                        <div style="position: relative; height: 250px; width: 100%;">
                            <canvas id="tvpiChart"></canvas>
                        </div>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h4>IRR Over Time</h4>
                        <div style="position: relative; height: 250px; width: 100%;">
                            <canvas id="irrChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Deployment Strategy Scenarios -->
                <div style="margin: 30px 0;">
                    <h3>🎯 Deployment Strategy Analysis</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;">
                        <div class="scenario-card" data-scenario="stop" style="padding: 20px; border: 2px solid #ddd; border-radius: 8px; cursor: pointer; background: white;">
                            <h4 style="color: #ea4335; margin: 0 0 10px 0;">🛑 Stop Investing</h4>
                            <p style="margin: 0; font-size: 14px; color: #666;">Focus 80% on follow-ons in existing winners</p>
                        </div>
                        <div class="scenario-card" data-scenario="conservative" style="padding: 20px; border: 2px solid #ddd; border-radius: 8px; cursor: pointer; background: white;">
                            <h4 style="color: #fbbc04; margin: 0 0 10px 0;">⚖️ Conservative</h4>
                            <p style="margin: 0; font-size: 14px; color: #666;">50% new investments, 50% follow-ons</p>
                        </div>
                        <div class="scenario-card" data-scenario="aggressive" style="padding: 20px; border: 2px solid #ddd; border-radius: 8px; cursor: pointer; background: white;">
                            <h4 style="color: #34a853; margin: 0 0 10px 0;">🚀 Aggressive</h4>
                            <p style="margin: 0; font-size: 14px; color: #666;">80% new investments for diversification</p>
                        </div>
                        <div class="scenario-card selected" data-scenario="followon" style="padding: 20px; border: 2px solid #1a73e8; border-radius: 8px; cursor: pointer; background: #e8f0fe;">
                            <h4 style="color: #1a73e8; margin: 0 0 10px 0;">💎 Follow-On Focused</h4>
                            <p style="margin: 0; font-size: 14px; color: #666;">70% follow-ons in proven winners</p>
                        </div>
                    </div>
                </div>

                <!-- Forecast Charts -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 30px 0;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h4>TVPI Forecast by Strategy</h4>
                        <div style="position: relative; height: 300px; width: 100%;">
                            <canvas id="forecastTVPIChart"></canvas>
                        </div>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h4>IRR Forecast by Strategy</h4>
                        <div style="position: relative; height: 300px; width: 100%;">
                            <canvas id="forecastIRRChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Distribution Chart -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 30px 0;">
                    <h4>📊 Net TVPI Distribution by Strategy</h4>
                    <div style="position: relative; height: 400px; width: 100%;">
                        <canvas id="distributionChart"></canvas>
                    </div>
                    <p style="font-size: 0.9em; color: #666; margin-top: 10px;">
                        <strong>What this shows:</strong> Probability density of Net TVPI outcomes for each strategy.
                        Higher peaks = more concentrated outcomes, longer tails = higher upside potential.
                    </p>
                </div>

                <!-- Analysis Results -->
                <div id="scenarioAnalysis" style="margin: 30px 0;">
                    <h3>📈 Scenario Analysis Results</h3>
                    <div id="scenarioBreakdown"></div>
                </div>

                <button onclick="runMonteCarloAnalysis()" style="background: #1a73e8; color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 20px 0;">
                    🎲 Run Monte Carlo Analysis (100,000 simulations)
                </button>
                
                <!-- Monte Carlo Explanation -->
                <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #1a73e8;">
                    <h3>🎲 How the Monte Carlo Simulation Works</h3>
                    <p><strong>The simulation runs 10,000 different possible futures for your portfolio over the next 5 years.</strong></p>
                    
                    <div style="margin: 20px 0;">
                        <h4>📊 Step 1: Starting Point (Your Current Portfolio)</h4>
                        <ul>
                            <li><strong>Uses your actual data:</strong> Takes current investments, valuations, and performance from your quarterly marks</li>
                            <li><strong>Calculates real metrics:</strong> Current Net TVPI, IRR, management fees called, remaining dry powder</li>
                            <li><strong>Fund parameters:</strong> Uses your fund size, vintage, management fees (2%), and carry (20%)</li>
                        </ul>
                    </div>
                    
                                         <div style="margin: 20px 0;">
                         <h4>🔮 Step 2: Project Existing Investments (5 Years Forward)</h4>
                         <p><strong>Each company progresses through stage-specific return distributions:</strong></p>
                         <ul>
                             <li><strong>Stage-based returns:</strong> Uses the same mathematical model as portfolio optimizer</li>
                             <li><strong>Mixture model:</strong> Log-normal distribution (typical outcomes) + Pareto tail (extreme winners)</li>
                             <li><strong>Stage progression:</strong> Each company advances 2-3 stages over 5 years</li>
                             <li><strong>Failure rates by stage:</strong> Seed 65% → Series A 35% → Series B 20% → Series C 10% → Growth 5%</li>
                         </ul>
                         <div style="background: white; padding: 10px; border-radius: 4px; margin: 10px 0;">
                             <strong>Your portfolio examples:</strong><br>
                             • <strong>Lambda (Series B):</strong> Progresses through Series C → Growth distributions<br>
                             • <strong>Torus (Series A):</strong> Progresses through Series B → Series C → Growth<br>
                             • <strong>Prime Trust (Dead):</strong> Stays at 0x (no recovery)<br>
                         </div>
                         <p><em>Each simulation draws from the actual return distributions, creating realistic power-law outcomes.</em></p>
                     </div>
                    
                    <div style="margin: 20px 0;">
                        <h4>💰 Step 3: Deploy Remaining Capital (Strategy-Dependent)</h4>
                        <p><strong>Your $211M dry powder gets allocated based on the selected strategy:</strong></p>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 10px 0;">
                            <div style="background: white; padding: 10px; border-radius: 4px;">
                                <strong>🛑 Stop Investing:</strong> 0% new, 80% follow-ons
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 4px;">
                                <strong>⚖️ Conservative:</strong> 50% new, 50% follow-ons
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 4px;">
                                <strong>🚀 Aggressive:</strong> 80% new, 20% follow-ons
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 4px;">
                                <strong>💎 Follow-On Focused:</strong> 30% new, 70% follow-ons
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin: 20px 0;">
                        <h4>🎯 Step 4: Follow-On Investment Logic</h4>
                        <ul>
                            <li><strong>Eligibility:</strong> Only companies currently performing ≥1.5x get follow-ons</li>
                            <li><strong>Success rate:</strong> 75% chance the follow-on is successful</li>
                            <li><strong>Follow-on size:</strong> Max 0.5x of original investment, split among winners</li>
                            <li><strong>Returns:</strong> Successful follow-ons return 2x-6x (randomly selected)</li>
                            <li><strong>Reality check:</strong> Based on your current portfolio, ~4-5 companies qualify for follow-ons</li>
                        </ul>
                    </div>
                    
                                         <div style="margin: 20px 0;">
                         <h4>🆕 Step 5: New Investment Logic</h4>
                         <ul>
                             <li><strong>Check size:</strong> $20M average (Series B-style investments)</li>
                             <li><strong>Portfolio construction:</strong> Remaining new capital ÷ $20M = number of new deals</li>
                             <li><strong>Stage entry:</strong> New investments enter at Series B stage</li>
                             <li><strong>Return distribution:</strong> Uses Series B stage parameters [20% failure, log-normal + Pareto tail]</li>
                             <li><strong>Stage progression:</strong> Series B → Series C → Growth over 5 years</li>
                             <li><strong>Power-law outcomes:</strong> Most fail or return <1x, few return 10x+</li>
                         </ul>
                     </div>
                    
                    <div style="margin: 20px 0;">
                        <h4>💼 Step 6: Calculate Final Net Returns</h4>
                        <ol>
                            <li><strong>Add management fees:</strong> 5 more years of 2% annual fees on fund size</li>
                            <li><strong>Calculate gross returns:</strong> Sum all investment values</li>
                            <li><strong>Calculate profits:</strong> Gross returns - total capital called (investments + fees)</li>
                            <li><strong>Apply carry:</strong> 20% of profits go to GP</li>
                            <li><strong>Net TVPI:</strong> (Gross returns - carry) ÷ total capital called</li>
                            <li><strong>Net IRR:</strong> Annualized return over full fund life</li>
                        </ol>
                    </div>
                    
                    <div style="margin: 20px 0; padding: 15px; background: #e8f0fe; border-radius: 6px;">
                        <h4>📈 Step 7: Statistical Analysis</h4>
                        <p><strong>After 10,000 simulations, we calculate:</strong></p>
                        <ul>
                            <li><strong>Mean/Median Net TVPI:</strong> Average and middle outcomes</li>
                            <li><strong>P25-P75 Range:</strong> 50% of outcomes fall in this range (confidence interval)</li>
                            <li><strong>P(Net TVPI ≥ 2x):</strong> Probability of achieving 2x+ net returns</li>
                            <li><strong>Mean Net IRR:</strong> Average annual returns after fees and carry</li>
                        </ul>
                        <p><em>This gives you a probabilistic view of each strategy's likely outcomes.</em></p>
                    </div>
                    
                    <div style="margin: 20px 0; padding: 15px; background: #dcfce7; border-radius: 6px;">
                        <h4>🎯 Why This Matters</h4>
                        <p><strong>The simulation answers:</strong> "Given my current portfolio performance and remaining capital, what's the best way to deploy my dry powder to maximize returns while managing risk?"</p>
                        <p>Each strategy trades off diversification (new investments) vs. concentration (follow-ons in proven winners).</p>
                    </div>
                </div>
            </div>
        </div>

        <div id="portfolioSummary" style="display:none; margin-top: 30px;">
            <!-- Portfolio summary will be shown here -->
        </div>
    </div>

    <script>
        let portfolioData = {};
        let currentCompany = null;

        // Sample data structure shows the realistic multi-round approach
        const sampleData = {
            "Prime Trust": {
                investments: [
                    {
                        id: 1,
                        date: "2021-10-01",
                        round: "Series A",
                        amount: 30000000,
                        ownership: 15.0,
                        preMoney: *********,
                        postMoney: *********,
                        notes: "Series A lead investment"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q4 2021",
                        enterpriseValue: *********,
                        carryingValue: 30000000,
                        revenue: 5000000,
                        growth: null,
                        notes: "Initial investment mark"
                    },
                    {
                        id: 2,
                        quarter: "Q1 2022",
                        enterpriseValue: 1********,
                        carryingValue: ********,
                        revenue: 4800000,
                        growth: -4,
                        notes: "Regulatory challenges emerging"
                    },
                    {
                        id: 3,
                        quarter: "Q2 2022",
                        enterpriseValue: ********,
                        carryingValue: 7500000,
                        revenue: 3000000,
                        growth: -37.5,
                        notes: "Banking crisis impact"
                    },
                    {
                        id: 4,
                        quarter: "Q3 2022",
                        enterpriseValue: 0,
                        carryingValue: 0,
                        revenue: 0,
                        growth: -100,
                        notes: "Company closure - total write-off"
                    }
                ]
            },
            "Klover": {
                investments: [
                    {
                        id: 1,
                        date: "2022-01-15",
                        round: "Series A",
                        amount: ********,
                        ownership: 12.0,
                        preMoney: ********,
                        postMoney: ********,
                        notes: "Series A investment"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q1 2022",
                        enterpriseValue: ********,
                        carryingValue: ********,
                        revenue: 2000000,
                        growth: null,
                        notes: "Post-investment valuation"
                    },
                    {
                        id: 2,
                        quarter: "Q2 2022",
                        enterpriseValue: 70000000,
                        carryingValue: 14000000,
                        revenue: 2100000,
                        growth: 5,
                        notes: "Slight market correction"
                    },
                    {
                        id: 3,
                        quarter: "Q3 2022",
                        enterpriseValue: ********,
                        carryingValue: ********,
                        revenue: 2300000,
                        growth: 9.5,
                        notes: "Stabilizing performance"
                    }
                ]
            },
            "Atomic": {
                investments: [
                    {
                        id: 1,
                        date: "2022-03-01",
                        round: "Series B",
                        amount: 20000000,
                        ownership: 8.5,
                        preMoney: 180000000,
                        postMoney: 235000000,
                        notes: "Series B participation"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q1 2022",
                        enterpriseValue: 235000000,
                        carryingValue: 20000000,
                        revenue: 8000000,
                        growth: null,
                        notes: "Post Series B mark"
                    },
                    {
                        id: 2,
                        quarter: "Q2 2022",
                        enterpriseValue: 235000000,
                        carryingValue: 20000000,
                        revenue: 8500000,
                        growth: 6.25,
                        notes: "Steady growth"
                    }
                ]
            },
            "Lambda": {
                investments: [
                    {
                        id: 1,
                        date: "2022-06-01",
                        round: "Series B",
                        amount: 20000000,
                        ownership: 10.0,
                        preMoney: 180000000,
                        postMoney: *********,
                        notes: "Series B co-lead"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q2 2022",
                        enterpriseValue: *********,
                        carryingValue: 20000000,
                        revenue: 12000000,
                        growth: null,
                        notes: "Strong initial performance"
                    },
                    {
                        id: 2,
                        quarter: "Q3 2022",
                        enterpriseValue: 280000000,
                        carryingValue: 28000000,
                        revenue: ********,
                        growth: 25,
                        notes: "Accelerating growth"
                    },
                    {
                        id: 3,
                        quarter: "Q4 2022",
                        enterpriseValue: 3********,
                        carryingValue: 35000000,
                        revenue: 18000000,
                        growth: 20,
                        notes: "Strong momentum continue"
                    },
                    {
                        id: 4,
                        quarter: "Q1 2023",
                        enterpriseValue: 420000000,
                        carryingValue: 42000000,
                        revenue: 22000000,
                        growth: 22.2,
                        notes: "Market leading position"
                    }
                ]
            },
            "Torus": {
                investments: [
                    {
                        id: 1,
                        date: "2024-01-01",
                        round: "Series A",
                        amount: 3000000,
                        ownership: 8.0,
                        preMoney: 34500000,
                        postMoney: 37500000,
                        notes: "Recent Series A investment"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q1 2024",
                        enterpriseValue: 37500000,
                        carryingValue: 3000000,
                        revenue: 1500000,
                        growth: null,
                        notes: "Initial post-investment mark"
                    },
                    {
                        id: 2,
                        quarter: "Q2 2024",
                        enterpriseValue: 62500000,
                        carryingValue: 5000000,
                        revenue: 2200000,
                        growth: 46.7,
                        notes: "Strong early traction"
                    }
                ]
            },
            "Attain": {
                investments: [
                    {
                        id: 1,
                        date: "2022-04-01",
                        round: "Series A",
                        amount: ********,
                        ownership: 12.5,
                        preMoney: 105000000,
                        postMoney: 120000000,
                        notes: "Series A lead"
                    },
                    {
                        id: 2,
                        date: "2023-08-15",
                        round: "Series B",
                        amount: 8000000,  // 0.53x follow-on
                        ownership: 6.2,
                        preMoney: 192000000,
                        postMoney: 230000000,
                        notes: "Series B follow-on - strong performance"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q2 2022",
                        enterpriseValue: 120000000,
                        carryingValue: ********,
                        revenue: 3500000,
                        growth: null,
                        notes: "Post Series A"
                    },
                    {
                        id: 2,
                        quarter: "Q3 2022",
                        enterpriseValue: 1********,
                        carryingValue: 18750000,
                        revenue: 4200000,
                        growth: 20,
                        notes: "Growing strongly"
                    },
                    {
                        id: 3,
                        quarter: "Q4 2022",
                        enterpriseValue: 180000000,
                        carryingValue: ********,
                        revenue: 5100000,
                        growth: 21.4,
                        notes: "Consistent execution"
                    },
                    {
                        id: 4,
                        quarter: "Q1 2023",
                        enterpriseValue: *********,
                        carryingValue: 25000000,
                        revenue: 6000000,
                        growth: 17.6,
                        notes: "Preparing for Series B"
                    },
                    {
                        id: 5,
                        quarter: "Q3 2023",
                        enterpriseValue: 230000000,
                        carryingValue: 31680000, // Reflects $23M total invested
                        revenue: 7500000,
                        growth: 25,
                        notes: "Post Series B - strong momentum"
                    }
                ]
            },
            "Paytient": {
                investments: [
                    {
                        id: 1,
                        date: "2022-02-01",
                        round: "Series B",
                        amount: 12000000,
                        ownership: 9.2,
                        preMoney: 118000000,
                        postMoney: 130000000,
                        notes: "Series B investment"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q1 2022",
                        enterpriseValue: 130000000,
                        carryingValue: 12000000,
                        revenue: 6000000,
                        growth: null,
                        notes: "Post investment mark"
                    },
                    {
                        id: 2,
                        quarter: "Q2 2022",
                        enterpriseValue: 140000000,
                        carryingValue: 12923077,
                        revenue: 6800000,
                        growth: 13.3,
                        notes: "Solid growth trajectory"
                    },
                    {
                        id: 3,
                        quarter: "Q3 2022",
                        enterpriseValue: 160000000,
                        carryingValue: 14769231,
                        revenue: 7500000,
                        growth: 10.3,
                        notes: "Market expansion"
                    },
                    {
                        id: 4,
                        quarter: "Q4 2022",
                        enterpriseValue: 195000000,
                        carryingValue: 18000000,
                        revenue: 9200000,
                        growth: 22.7,
                        notes: "Strong finish to year"
                    }
                ]
            },
            "Cylinder": {
                investments: [
                    {
                        id: 1,
                        date: "2023-01-01",
                        round: "Series B",
                        amount: ********,
                        ownership: 11.5,
                        preMoney: 115500000,
                        postMoney: 130500000,
                        notes: "Series B participation"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q1 2023",
                        enterpriseValue: 130500000,
                        carryingValue: ********,
                        revenue: 4000000,
                        growth: null,
                        notes: "Initial investment"
                    },
                    {
                        id: 2,
                        quarter: "Q2 2023",
                        enterpriseValue: 130500000,
                        carryingValue: ********,
                        revenue: 4100000,
                        growth: 2.5,
                        notes: "Slow growth concerns"
                    },
                    {
                        id: 3,
                        quarter: "Q3 2023",
                        enterpriseValue: 117450000,
                        carryingValue: 13500000,
                        revenue: 4000000,
                        growth: -2.4,
                        notes: "Market headwinds"
                    },
                    {
                        id: 4,
                        quarter: "Q4 2023",
                        enterpriseValue: 97875000,
                        carryingValue: 11250000,
                        revenue: 3800000,
                        growth: -5,
                        notes: "Declining performance"
                    }
                ]
            },
            "TechCorp Inc": {
                investments: [
                    {
                        id: 1,
                        date: "2020-03-15",
                        round: "Seed",
                        amount: 2000000,
                        ownership: 8.5,
                        preMoney: 18000000,
                        postMoney: 25000000,
                        notes: "Initial seed investment"
                    },
                    {
                        id: 2,
                        date: "2021-08-20",
                        round: "Series A",
                        amount: 1000000,  // 0.5x follow-on, not 2.0x
                        ownership: 3.2,
                        preMoney: 35000000,
                        postMoney: ********,
                        notes: "Series A follow-on - conservative sizing"
                    },
                    {
                        id: 3,
                        date: "2022-11-10",
                        round: "Series B",
                        amount: 1600000,  // 0.8x follow-on
                        ownership: 2.1,
                        preMoney: ********,
                        postMoney: 90000000,
                        notes: "Series B follow-on - strong performance"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q1 2020",
                        enterpriseValue: 25000000,
                        carryingValue: 2000000,
                        revenue: 500000,
                        growth: null,
                        notes: "Initial valuation post-seed"
                    },
                    {
                        id: 2,
                        quarter: "Q2 2020",
                        enterpriseValue: 30000000,
                        carryingValue: 2400000,
                        revenue: 750000,
                        growth: 50,
                        notes: "Strong Q2 growth"
                    },
                    {
                        id: 3,
                        quarter: "Q3 2020",
                        enterpriseValue: 35000000,
                        carryingValue: 2800000,
                        revenue: 900000,
                        growth: 20,
                        notes: "Continued momentum"
                    },
                    {
                        id: 4,
                        quarter: "Q4 2020",
                        enterpriseValue: 40000000,
                        carryingValue: 3200000,
                        revenue: 1200000,
                        growth: 33.3,
                        notes: "Strong end to year"
                    },
                    {
                        id: 5,
                        quarter: "Q1 2021",
                        enterpriseValue: 45000000,
                        carryingValue: 3600000,
                        revenue: 1500000,
                        growth: 25,
                        notes: "Pre-Series A momentum"
                    },
                    {
                        id: 6,
                        quarter: "Q2 2021",
                        enterpriseValue: ********,
                        carryingValue: 3800000, // Reflects total investment of $3M
                        revenue: 2000000,
                        growth: 33.3,
                        notes: "Post-Series A - conservative mark"
                    },
                    {
                        id: 7,
                        quarter: "Q3 2021",
                        enterpriseValue: 60000000,
                        carryingValue: 4560000,
                        revenue: 2500000,
                        growth: 25,
                        notes: "Outperforming expectations"
                    },
                    {
                        id: 8,
                        quarter: "Q4 2021",
                        enterpriseValue: 70000000,
                        carryingValue: 5320000,
                        revenue: 3200000,
                        growth: 28,
                        notes: "Strong growth trajectory"
                    },
                    {
                        id: 9,
                        quarter: "Q1 2022",
                        enterpriseValue: 80000000,
                        carryingValue: 6080000,
                        revenue: 4000000,
                        growth: 25,
                        notes: "Preparing for Series B"
                    },
                    {
                        id: 10,
                        quarter: "Q2 2022",
                        enterpriseValue: 90000000,
                        carryingValue: 6840000,
                        revenue: 5000000,
                        growth: 25,
                        notes: "Series B momentum building"
                    },
                    {
                        id: 11,
                        quarter: "Q3 2022",
                        enterpriseValue: 95000000,
                        carryingValue: 7220000,
                        revenue: 6000000,
                        growth: 20,
                        notes: "Pre-Series B valuation"
                    },
                    {
                        id: 12,
                        quarter: "Q4 2022",
                        enterpriseValue: 120000000,
                        carryingValue: 8280000, // Reflects $4.6M total invested
                        revenue: 7500000,
                        growth: 25,
                        notes: "Post-Series B mark"
                    }
                ]
            },
            "DataFlow Solutions": {
                investments: [
                    {
                        id: 1,
                        date: "2019-06-10",
                        round: "Series A",
                        amount: 8000000,
                        ownership: 15.2,
                        preMoney: 42000000,
                        postMoney: 60000000,
                        notes: "Series A lead investment"
                    },
                    {
                        id: 2,
                        date: "2021-03-15",
                        round: "Series B",
                        amount: 4800000, // 0.6x follow-on
                        ownership: 8.1,
                        preMoney: 90000000,
                        postMoney: *********,
                        notes: "Series B follow-on - moderate sizing"
                    }
                ],
                quarterlyMarks: [
                    {
                        id: 1,
                        quarter: "Q3 2019",
                        enterpriseValue: 60000000,
                        carryingValue: 8000000,
                        revenue: 3000000,
                        growth: null,
                        notes: "Initial Series A valuation"
                    },
                    {
                        id: 2,
                        quarter: "Q4 2019",
                        enterpriseValue: 55000000,
                        carryingValue: 7333333,
                        revenue: 3200000,
                        growth: 6.7,
                        notes: "Market correction"
                    },
                    {
                        id: 3,
                        quarter: "Q1 2020",
                        enterpriseValue: 65000000,
                        carryingValue: 8666667,
                        revenue: 3800000,
                        growth: 18.8,
                        notes: "Recovery and growth"
                    },
                    {
                        id: 4,
                        quarter: "Q2 2020",
                        enterpriseValue: ********,
                        carryingValue: 10000000,
                        revenue: 4500000,
                        growth: 18.4,
                        notes: "Strong pandemic performance"
                    },
                    {
                        id: 5,
                        quarter: "Q1 2021",
                        enterpriseValue: 100000000,
                        carryingValue: 10666667, // Pre-Series B
                        revenue: 6000000,
                        growth: 25,
                        notes: "Series B preparation"
                    },
                    {
                        id: 6,
                        quarter: "Q2 2021",
                        enterpriseValue: *********,
                        carryingValue: 14464000, // Post-Series B, total $12.8M invested
                        revenue: 7200000,
                        growth: 20,
                        notes: "Post-Series B mark"
                    }
                ]
            }
        };

        function loadSampleData() {
            portfolioData = JSON.parse(JSON.stringify(sampleData));
            updateCompanySelector();
            if (Object.keys(portfolioData).length > 0) {
                selectCompany(Object.keys(portfolioData)[0]);
            }
        }

        function updateCompanySelector() {
            const selector = document.getElementById('companySelect');
            selector.innerHTML = '<option value="">Select Company...</option>';
            
            Object.keys(portfolioData).forEach(companyName => {
                const option = document.createElement('option');
                option.value = companyName;
                option.textContent = companyName;
                selector.appendChild(option);
            });
        }

        function selectCompany(companyName = null) {
            if (!companyName) {
                companyName = document.getElementById('companySelect').value;
            }
            
            if (!companyName || !portfolioData[companyName]) {
                document.getElementById('companyView').style.display = 'none';
                return;
            }

            currentCompany = companyName;
            document.getElementById('companySelect').value = companyName;
            document.getElementById('companyView').style.display = 'block';
            
            updateCompanyView();
        }

        function addNewCompany() {
            const companyName = document.getElementById('newCompanyName').value.trim();
            if (!companyName) {
                alert('Please enter a company name');
                return;
            }

            if (portfolioData[companyName]) {
                alert('Company already exists');
                return;
            }

            portfolioData[companyName] = {
                investments: [],
                quarterlyMarks: []
            };

            document.getElementById('newCompanyName').value = '';
            updateCompanySelector();
            selectCompany(companyName);
        }

        function updateCompanyView() {
            const company = portfolioData[currentCompany];
            
            document.getElementById('companyTitle').textContent = currentCompany;
            
            // Calculate summary
            const totalInvested = company.investments.reduce((sum, inv) => sum + inv.amount, 0);
            const latestMark = company.quarterlyMarks.length > 0 ? 
                company.quarterlyMarks[company.quarterlyMarks.length - 1] : null;
            
            document.getElementById('totalInvested').textContent = `$${(totalInvested / 1e6).toFixed(1)}M`;
            
            let summaryText = `${company.investments.length} investment rounds`;
            if (latestMark) {
                const currentValue = latestMark.carryingValue;
                const multiple = currentValue / totalInvested;
                summaryText += ` • Latest: ${multiple.toFixed(1)}x • ${company.quarterlyMarks.length} quarterly marks`;
            }
            document.getElementById('companySummary').textContent = summaryText;

            renderTimeline();
            updateSummaryStats();
        }

        function renderTimeline() {
            const company = portfolioData[currentCompany];
            const timeline = document.getElementById('timeline');
            timeline.innerHTML = '';

            // Combine and sort all events by date
            const allEvents = [];
            
            company.investments.forEach(inv => {
                allEvents.push({
                    type: 'investment',
                    date: new Date(inv.date),
                    data: inv
                });
            });

            company.quarterlyMarks.forEach(mark => {
                // Convert quarter to approximate date for sorting
                const [quarter, year] = mark.quarter.split(' ');
                const monthMap = { 'Q1': '03', 'Q2': '06', 'Q3': '09', 'Q4': '12' };
                const approxDate = new Date(`${year}-${monthMap[quarter]}-15`);
                
                allEvents.push({
                    type: 'mark',
                    date: approxDate,
                    data: mark
                });
            });

            // Sort by date (newest first)
            allEvents.sort((a, b) => b.date - a.date);

            allEvents.forEach(event => {
                const item = document.createElement('div');
                item.className = `timeline-item ${event.type === 'investment' ? 'investment-item' : ''}`;
                
                if (event.type === 'investment') {
                    item.innerHTML = renderInvestmentItem(event.data);
                } else {
                    item.innerHTML = renderQuarterlyMarkItem(event.data);
                }
                
                timeline.appendChild(item);
            });
        }

        function renderInvestmentItem(investment) {
            return `
                <div class="investment-header">
                    💰 ${investment.round} Investment - ${new Date(investment.date).toLocaleDateString()}
                    <button class="remove-item" onclick="removeInvestment(${investment.id})">✕ Remove</button>
                </div>
                <div class="data-grid">
                    <div class="data-item">
                        <label>Investment Amount</label>
                        <input type="number" value="${investment.amount}" 
                               onchange="updateInvestment(${investment.id}, 'amount', this.value)">
                    </div>
                    <div class="data-item">
                        <label>Ownership %</label>
                        <input type="number" step="0.1" value="${investment.ownership}" 
                               onchange="updateInvestment(${investment.id}, 'ownership', this.value)">
                    </div>
                    <div class="data-item">
                        <label>Pre-Money Valuation</label>
                        <input type="number" value="${investment.preMoney}" 
                               onchange="updateInvestment(${investment.id}, 'preMoney', this.value)">
                    </div>
                    <div class="data-item">
                        <label>Post-Money Valuation</label>
                        <input type="number" value="${investment.postMoney}" 
                               onchange="updateInvestment(${investment.id}, 'postMoney', this.value)">
                    </div>
                    <div class="data-item">
                        <label>Round Type</label>
                        <select onchange="updateInvestment(${investment.id}, 'round', this.value)">
                            <option value="Seed" ${investment.round === 'Seed' ? 'selected' : ''}>Seed</option>
                            <option value="Series A" ${investment.round === 'Series A' ? 'selected' : ''}>Series A</option>
                            <option value="Series B" ${investment.round === 'Series B' ? 'selected' : ''}>Series B</option>
                            <option value="Series C" ${investment.round === 'Series C' ? 'selected' : ''}>Series C</option>
                            <option value="Growth" ${investment.round === 'Growth' ? 'selected' : ''}>Growth</option>
                        </select>
                    </div>
                    <div class="data-item">
                        <label>Investment Date</label>
                        <input type="date" value="${investment.date}" 
                               onchange="updateInvestment(${investment.id}, 'date', this.value)">
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #666; font-weight: 600;">Notes</label>
                    <input type="text" value="${investment.notes || ''}" 
                           onchange="updateInvestment(${investment.id}, 'notes', this.value)"
                           style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
            `;
        }

        function renderQuarterlyMarkItem(mark) {
            const totalInvestedForQuarter = getTotalInvestedForQuarter(mark.quarter);
            const multiple = totalInvestedForQuarter > 0 ? mark.carryingValue / totalInvestedForQuarter : 0;
            const evMultiple = totalInvestedForQuarter > 0 ? mark.enterpriseValue / totalInvestedForQuarter : 0;
            
            let performanceClass = 'performing-ok';
            let performanceText = 'Stable';
            
            if (multiple >= 2) {
                performanceClass = 'performing-well';
                performanceText = 'Strong';
            } else if (multiple < 1) {
                performanceClass = 'performing-poor';
                performanceText = 'Below Cost';
            }

            return `
                <div class="quarter-header">
                    📈 ${mark.quarter} Quarterly Mark
                    <span class="performance-indicator ${performanceClass}">${performanceText}</span>
                    <button class="remove-item" onclick="removeQuarterlyMark(${mark.id})">✕ Remove</button>
                </div>
                <div class="data-grid">
                    <div class="data-item">
                        <label>Quarter</label>
                        <input type="text" value="${mark.quarter}" 
                               onchange="updateQuarterlyMark(${mark.id}, 'quarter', this.value)">
                    </div>
                    <div class="data-item">
                        <label>Enterprise Value</label>
                        <input type="number" value="${mark.enterpriseValue}" 
                               onchange="updateQuarterlyMark(${mark.id}, 'enterpriseValue', this.value)">
                    </div>
                    <div class="data-item">
                        <label>Carrying Value</label>
                        <input type="number" value="${mark.carryingValue}" 
                               onchange="updateQuarterlyMark(${mark.id}, 'carryingValue', this.value)">
                    </div>
                    <div class="data-item">
                        <label>Revenue (Optional)</label>
                        <input type="number" value="${mark.revenue || ''}" 
                               onchange="updateQuarterlyMark(${mark.id}, 'revenue', this.value)">
                    </div>
                    <div class="data-item">
                        <label>Growth % QoQ</label>
                        <input type="number" step="0.1" value="${mark.growth || ''}" 
                               onchange="updateQuarterlyMark(${mark.id}, 'growth', this.value)">
                    </div>
                    <div class="data-item" style="grid-column: span 2;">
                        <label>Notes</label>
                        <input type="text" value="${mark.notes || ''}" 
                               onchange="updateQuarterlyMark(${mark.id}, 'notes', this.value)">
                    </div>
                </div>
                <div style="margin-top: 10px; font-size: 14px; color: #666;">
                    <strong>Carrying Multiple:</strong> ${multiple.toFixed(2)}x | 
                    <strong>EV Multiple:</strong> ${evMultiple.toFixed(2)}x |
                    <strong>Total Invested by ${mark.quarter}:</strong> $${(totalInvestedForQuarter / 1e6).toFixed(1)}M
                </div>
            `;
        }

        function getTotalInvestedForQuarter(quarter) {
            const company = portfolioData[currentCompany];
            const [q, year] = quarter.split(' ');
            const quarterMap = { 'Q1': 3, 'Q2': 6, 'Q3': 9, 'Q4': 12 };
            const quarterDate = new Date(`${year}-${quarterMap[q]}-15`);
            
            return company.investments
                .filter(inv => new Date(inv.date) <= quarterDate)
                .reduce((sum, inv) => sum + inv.amount, 0);
        }

        function addInvestment() {
            const company = portfolioData[currentCompany];
            const newId = Math.max(0, ...company.investments.map(i => i.id)) + 1;
            
            const newInvestment = {
                id: newId,
                date: new Date().toISOString().split('T')[0],
                round: 'Series A',
                amount: 1000000, // Default to smaller follow-on size
                ownership: 5.0,
                preMoney: 40000000,
                postMoney: ********,
                notes: 'Follow-on investment'
            };
            
            company.investments.push(newInvestment);
            updateCompanyView();
        }

        function addQuarterlyMark() {
            const company = portfolioData[currentCompany];
            const newId = Math.max(0, ...company.quarterlyMarks.map(m => m.id)) + 1;
            
            const currentYear = new Date().getFullYear();
            const currentQuarter = Math.ceil((new Date().getMonth() + 1) / 3);
            
            const newMark = {
                id: newId,
                quarter: `Q${currentQuarter} ${currentYear}`,
                enterpriseValue: 25000000,
                carryingValue: 5000000,
                revenue: null,
                growth: null,
                notes: ''
            };
            
            company.quarterlyMarks.push(newMark);
            updateCompanyView();
        }

        function updateInvestment(id, field, value) {
            const company = portfolioData[currentCompany];
            const investment = company.investments.find(i => i.id === id);
            if (investment) {
                investment[field] = field === 'date' || field === 'round' || field === 'notes' ? value : parseFloat(value) || 0;
                updateCompanyView();
            }
        }

        function updateQuarterlyMark(id, field, value) {
            const company = portfolioData[currentCompany];
            const mark = company.quarterlyMarks.find(m => m.id === id);
            if (mark) {
                if (field === 'quarter' || field === 'notes') {
                    mark[field] = value;
                } else {
                    mark[field] = value === '' ? null : parseFloat(value);
                }
                updateCompanyView();
            }
        }

        function removeInvestment(id) {
            if (confirm('Are you sure you want to remove this investment?')) {
                const company = portfolioData[currentCompany];
                company.investments = company.investments.filter(i => i.id !== id);
                updateCompanyView();
            }
        }

        function removeQuarterlyMark(id) {
            if (confirm('Are you sure you want to remove this quarterly mark?')) {
                const company = portfolioData[currentCompany];
                company.quarterlyMarks = company.quarterlyMarks.filter(m => m.id !== id);
                updateCompanyView();
            }
        }

        function updateSummaryStats() {
            const company = portfolioData[currentCompany];
            const statsContainer = document.getElementById('summaryStats');
            
            const totalInvested = company.investments.reduce((sum, inv) => sum + inv.amount, 0);
            const latestMark = company.quarterlyMarks.length > 0 ? 
                company.quarterlyMarks[company.quarterlyMarks.length - 1] : null;
            
            let statsHTML = `
                <div class="stat-card">
                    <div class="stat-value">$${(totalInvested / 1e6).toFixed(1)}M</div>
                    <div class="stat-label">Total Invested</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${company.investments.length}</div>
                    <div class="stat-label">Investment Rounds</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${company.quarterlyMarks.length}</div>
                    <div class="stat-label">Quarterly Marks</div>
                </div>
            `;
            
            if (latestMark && totalInvested > 0) {
                const currentMultiple = latestMark.carryingValue / totalInvested;
                const evMultiple = latestMark.enterpriseValue / totalInvested;
                
                statsHTML += `
                    <div class="stat-card">
                        <div class="stat-value">${currentMultiple.toFixed(2)}x</div>
                        <div class="stat-label">Current Multiple (Carrying)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${evMultiple.toFixed(2)}x</div>
                        <div class="stat-label">EV Multiple</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">$${(latestMark.carryingValue / 1e6).toFixed(1)}M</div>
                        <div class="stat-label">Current Value</div>
                    </div>
                `;
            }
            
            statsContainer.innerHTML = statsHTML;
        }

        function savePortfolio() {
            const data = JSON.stringify(portfolioData, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'portfolio-data.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        function exportToCSV() {
            let csv = 'Company,Type,Date,Quarter,Amount,Round,Ownership,Pre-Money,Post-Money,Enterprise Value,Carrying Value,Revenue,Growth,Multiple,EV Multiple,Notes\n';
            
            Object.keys(portfolioData).forEach(companyName => {
                const company = portfolioData[companyName];
                
                company.investments.forEach(inv => {
                    csv += `"${companyName}",Investment,"${inv.date}","","${inv.amount}","${inv.round}","${inv.ownership}","${inv.preMoney}","${inv.postMoney}","","","","","","","${inv.notes || ''}"\n`;
                });
                
                company.quarterlyMarks.forEach(mark => {
                    const totalInvested = getTotalInvestedForQuarter(mark.quarter);
                    const multiple = totalInvested > 0 ? (mark.carryingValue / totalInvested).toFixed(2) : '';
                    const evMultiple = totalInvested > 0 ? (mark.enterpriseValue / totalInvested).toFixed(2) : '';
                    
                    csv += `"${companyName}",QuarterlyMark,"","${mark.quarter}","","","","","","${mark.enterpriseValue}","${mark.carryingValue}","${mark.revenue || ''}","${mark.growth || ''}","${multiple}","${evMultiple}","${mark.notes || ''}"\n`;
                });
            });
            
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'portfolio-export.csv';
            a.click();
            URL.revokeObjectURL(url);
        }

        function showPortfolioSummary() {
            const summaryDiv = document.getElementById('portfolioSummary');
            summaryDiv.style.display = summaryDiv.style.display === 'none' ? 'block' : 'none';
            
            if (summaryDiv.style.display === 'block') {
                generatePortfolioSummary();
            }
        }

        function generateReport() {
            // Generate comprehensive portfolio report
            let totalInvested = 0;
            let totalCurrentValue = 0;
            let totalFollowOnInvestment = 0;
            let companiesCount = Object.keys(portfolioData).length;
            
            let reportHTML = `
                <div style="background: white; padding: 30px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h2>📋 Portfolio Performance Report</h2>
                    <p><em>Generated on ${new Date().toLocaleDateString()}</em></p>
                    
                    <h3>Executive Summary</h3>
                    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                        <thead>
                            <tr style="background-color: #f5f7fa;">
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Company</th>
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">Total Invested</th>
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">Current Value</th>
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">Multiple</th>
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Rounds</th>
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: center;">Status</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            // Analyze each company
            const companyAnalysis = [];
            Object.keys(portfolioData).forEach(companyName => {
                const company = portfolioData[companyName];
                const companyInvested = company.investments.reduce((sum, inv) => sum + inv.amount, 0);
                const latestMark = company.quarterlyMarks.length > 0 ? 
                    company.quarterlyMarks[company.quarterlyMarks.length - 1] : null;
                const currentValue = latestMark ? latestMark.carryingValue : companyInvested;
                const multiple = companyInvested > 0 ? currentValue / companyInvested : 0;
                
                // Calculate follow-ons
                const followOnInvestment = company.investments.length > 1 ? 
                    company.investments.slice(1).reduce((sum, inv) => sum + inv.amount, 0) : 0;
                
                totalInvested += companyInvested;
                totalCurrentValue += currentValue;
                totalFollowOnInvestment += followOnInvestment;
                
                let statusClass = '';
                let statusText = '';
                if (multiple >= 2) {
                    statusClass = 'style="color: #16a34a; font-weight: bold;"';
                    statusText = 'Strong';
                } else if (multiple >= 1) {
                    statusClass = 'style="color: #ca8a04; font-weight: bold;"';
                    statusText = 'Stable';
                } else {
                    statusClass = 'style="color: #dc2626; font-weight: bold;"';
                    statusText = 'At Risk';
                }
                
                reportHTML += `
                    <tr>
                        <td style="padding: 12px; border: 1px solid #ddd;"><strong>${companyName}</strong></td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right;">$${(companyInvested / 1e6).toFixed(1)}M</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right;">$${(currentValue / 1e6).toFixed(1)}M</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right; font-weight: bold;">${multiple.toFixed(2)}x</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: center;">${company.investments.length}</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: center;" ${statusClass}>${statusText}</td>
                    </tr>
                `;
                
                companyAnalysis.push({
                    name: companyName,
                    invested: companyInvested,
                    currentValue: currentValue,
                    multiple: multiple,
                    rounds: company.investments.length,
                    followOns: followOnInvestment,
                    status: statusText
                });
            });
            
            const portfolioMultiple = totalInvested > 0 ? totalCurrentValue / totalInvested : 0;
            const followOnPercent = totalInvested > 0 ? (totalFollowOnInvestment / totalInvested) * 100 : 0;
            const winners = companyAnalysis.filter(c => c.multiple >= 2).length;
            const stable = companyAnalysis.filter(c => c.multiple >= 1 && c.multiple < 2).length;
            const atRisk = companyAnalysis.filter(c => c.multiple < 1).length;
            
            reportHTML += `
                        </tbody>
                    </table>
                    
                    <h3>Portfolio Metrics</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4f46e5;">${companiesCount}</div>
                            <div>Portfolio Companies</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4f46e5;">$${(totalInvested / 1e6).toFixed(1)}M</div>
                            <div>Total Invested</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4f46e5;">$${(totalCurrentValue / 1e6).toFixed(1)}M</div>
                            <div>Current Value</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4f46e5;">${portfolioMultiple.toFixed(2)}x</div>
                            <div>Portfolio Multiple</div>
                        </div>
                    </div>
                    
                    <h3>Performance Breakdown</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;">
                        <div style="background: #dcfce7; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #16a34a;">${winners}</div>
                            <div>Winners (≥2x)</div>
                        </div>
                        <div style="background: #fef3c7; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #ca8a04;">${stable}</div>
                            <div>Stable (1-2x)</div>
                        </div>
                        <div style="background: #fee2e2; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #dc2626;">${atRisk}</div>
                            <div>At Risk (<1x)</div>
                        </div>
                        <div style="background: #f1f5f9; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 20px; font-weight: bold; color: #64748b;">${followOnPercent.toFixed(1)}%</div>
                            <div>Follow-On %</div>
                        </div>
                    </div>
                    
                    <h3>Follow-On Analysis</h3>
                    <p><strong>Total Follow-On Investment:</strong> $${(totalFollowOnInvestment / 1e6).toFixed(1)}M (${followOnPercent.toFixed(1)}% of total)</p>
                    <p><strong>Companies with Follow-Ons:</strong> ${companyAnalysis.filter(c => c.followOns > 0).length} out of ${companiesCount}</p>
                    
                    <h3>Recommendations</h3>
                    <ul>
            `;
            
            if (winners > 0) {
                reportHTML += `<li><strong>Continue Supporting Winners:</strong> ${winners} companies are performing strongly (≥2x). Consider additional follow-on investments.</li>`;
            }
            
            if (atRisk > 0) {
                const atRiskCapital = companyAnalysis.filter(c => c.multiple < 1).reduce((sum, c) => sum + c.invested, 0);
                reportHTML += `<li><strong>Address Underperformers:</strong> ${atRisk} companies ($${(atRiskCapital / 1e6).toFixed(1)}M) are below cost. Avoid additional investment and consider exit strategies.</li>`;
            }
            
            if (followOnPercent < 20) {
                reportHTML += `<li><strong>Follow-On Strategy:</strong> Current follow-on rate (${followOnPercent.toFixed(1)}%) is below optimal range (20-40%). Consider increasing follow-on reserves.</li>`;
            } else if (followOnPercent > 40) {
                reportHTML += `<li><strong>Portfolio Diversification:</strong> Follow-on rate (${followOnPercent.toFixed(1)}%) is high. Balance with new investments for diversification.</li>`;
            }
            
            reportHTML += `
                    </ul>
                    
                    <div style="margin-top: 30px; padding: 15px; background-color: #f1f5f9; border-radius: 8px; font-size: 12px; color: #64748b;">
                        <p><strong>Report Notes:</strong></p>
                        <ul>
                            <li>Values based on latest quarterly marks where available</li>
                            <li>Multiple calculated as Current Value ÷ Total Invested</li>
                            <li>Follow-on investments include all rounds after initial investment</li>
                            <li>This analysis is for internal use and should not be shared externally</li>
                        </ul>
                    </div>
                </div>
            `;
            
            // Show the report
            const summaryDiv = document.getElementById('portfolioSummary');
            summaryDiv.innerHTML = reportHTML;
            summaryDiv.style.display = 'block';
            
            // Scroll to report
            summaryDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function generatePortfolioSummary() {
            const summaryDiv = document.getElementById('portfolioSummary');
            let totalInvested = 0;
            let totalCurrentValue = 0;
            let companiesCount = Object.keys(portfolioData).length;
            
            let summaryHTML = '<h2>📊 Portfolio Summary</h2><div class="summary-stats">';
            
            Object.keys(portfolioData).forEach(companyName => {
                const company = portfolioData[companyName];
                const companyInvested = company.investments.reduce((sum, inv) => sum + inv.amount, 0);
                const latestMark = company.quarterlyMarks.length > 0 ? 
                    company.quarterlyMarks[company.quarterlyMarks.length - 1] : null;
                
                totalInvested += companyInvested;
                if (latestMark) {
                    totalCurrentValue += latestMark.carryingValue;
                }
            });
            
            const portfolioMultiple = totalInvested > 0 ? totalCurrentValue / totalInvested : 0;
            
            summaryHTML += `
                <div class="stat-card">
                    <div class="stat-value">${companiesCount}</div>
                    <div class="stat-label">Portfolio Companies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$${(totalInvested / 1e6).toFixed(1)}M</div>
                    <div class="stat-label">Total Invested</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$${(totalCurrentValue / 1e6).toFixed(1)}M</div>
                    <div class="stat-label">Current Value</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${portfolioMultiple.toFixed(2)}x</div>
                    <div class="stat-label">Portfolio Multiple</div>
                </div>
            </div>`;
            
            summaryDiv.innerHTML = summaryHTML;
        }

        // Monte Carlo Forecasting Functions
        let selectedScenario = 'followon';
        let tvpiChart = null;
        let irrChart = null;
        let forecastTVPIChart = null;
        let forecastIRRChart = null;
        let distributionChart = null;
        let chartsInitialized = false;

        function showForecasting() {
            const forecastingDiv = document.getElementById('forecastingSection');
            forecastingDiv.style.display = forecastingDiv.style.display === 'none' ? 'block' : 'none';
            
            if (forecastingDiv.style.display === 'block') {
                if (!chartsInitialized) {
                    calculateCurrentPerformance();
                    generateHistoricalCharts();
                    setupScenarioCards();
                    chartsInitialized = true;
                }
                forecastingDiv.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function calculateCurrentPerformance() {
            // Get fund parameters
            const fundSize = parseFloat(document.getElementById('fundSize').value) * 1e6;
            const fundVintage = parseInt(document.getElementById('fundVintage').value);
            const managementFeeRate = parseFloat(document.getElementById('managementFee').value) / 100;
            const carryRate = parseFloat(document.getElementById('carriedInterest').value) / 100;

            // Calculate portfolio totals
            let totalInvested = 0;
            let totalCurrentValue = 0;
            
            Object.keys(portfolioData).forEach(companyName => {
                const company = portfolioData[companyName];
                const companyInvested = company.investments.reduce((sum, inv) => sum + inv.amount, 0);
                const latestMark = company.quarterlyMarks.length > 0 ? 
                    company.quarterlyMarks[company.quarterlyMarks.length - 1] : null;
                const currentValue = latestMark ? latestMark.carryingValue : companyInvested;
                
                totalInvested += companyInvested;
                totalCurrentValue += currentValue;
            });

            // Calculate management fees called
            const currentDate = new Date();
            const fundStartDate = new Date(fundVintage, 0, 1);
            const quartersElapsed = Math.floor((currentDate - fundStartDate) / (1000 * 60 * 60 * 24 * 91.25));
            const managementFeesPerQuarter = (fundSize * managementFeeRate) / 4;
            const totalManagementFeesCalled = Math.min(quartersElapsed * managementFeesPerQuarter, fundSize * managementFeeRate * 10);
            
            // Called capital
            const calledCapital = totalInvested + totalManagementFeesCalled;
            
            // TVPI calculations
            const grossTVPI = calledCapital > 0 ? totalCurrentValue / calledCapital : 0;
            
            // Net calculations
            const totalProfits = Math.max(0, totalCurrentValue - calledCapital);
            const carryOnProfits = totalProfits * carryRate;
            const netValue = totalCurrentValue - carryOnProfits;
            const netTVPI = calledCapital > 0 ? netValue / calledCapital : 0;
            
            // IRR calculation
            const yearsElapsed = Math.max(0.25, (currentDate - fundStartDate) / (365.25 * 24 * 60 * 60 * 1000));
            const currentIRR = netTVPI > 0 ? (Math.pow(netTVPI, 1/yearsElapsed) - 1) * 100 : 0;
            
            // Update display
            document.getElementById('currentGrossTVPI').textContent = `${grossTVPI.toFixed(2)}x`;
            document.getElementById('currentNetTVPI').textContent = `${netTVPI.toFixed(2)}x`;
            document.getElementById('currentIRR').textContent = `${currentIRR.toFixed(1)}%`;
            document.getElementById('remainingCapital').textContent = `$${((fundSize - calledCapital) / 1e6).toFixed(0)}M`;
            document.getElementById('deploymentRate').textContent = `${((calledCapital / fundSize) * 100).toFixed(1)}%`;

            return {
                grossTVPI,
                netTVPI,
                currentIRR: currentIRR / 100,
                totalInvested,
                totalCurrentValue,
                calledCapital,
                remainingCapital: fundSize - calledCapital,
                fundSize,
                managementFeeRate,
                carryRate,
                yearsElapsed
            };
        }

                 function generateHistoricalCharts() {
             const performance = calculateCurrentPerformance();
             
             // Generate realistic historical progression
             const quarters = Math.max(8, Math.min(16, Math.floor(performance.yearsElapsed * 4) + 1));
             const labels = [];
             const tvpiData = [];
             const irrData = [];
             
             const fundVintage = parseInt(document.getElementById('fundVintage').value);
             
             // Create realistic J-curve progression leading to current performance
             for (let q = 0; q < quarters; q++) {
                 const year = fundVintage + Math.floor(q / 4);
                 const quarter = (q % 4) + 1;
                 labels.push(`Q${quarter} ${year}`);
                 
                 if (q === quarters - 1) {
                     // Current quarter - use actual values
                     tvpiData.push(performance.netTVPI);
                     irrData.push(performance.currentIRR * 100);
                 } else {
                     // Historical progression - realistic J-curve
                     const progress = q / (quarters - 1);
                     const currentTVPI = performance.netTVPI;
                     
                     let tvpi;
                     if (currentTVPI < 0.8) {
                         // Poor performing fund - gradual decline to current
                         tvpi = 0.95 - (progress * 0.4) + (progress * (currentTVPI - 0.55));
                     } else if (currentTVPI < 1.2) {
                         // Moderate fund - typical J-curve to current
                         tvpi = 0.9 - (progress * 0.2) + (progress * progress * (currentTVPI - 0.7));
                     } else {
                         // Good fund - classic J-curve
                         tvpi = 0.8 - (progress * 0.1) + (progress * progress * (currentTVPI - 0.7));
                     }
                     
                     // Calculate realistic IRR based on TVPI and time
                     const years = Math.max(0.25, (q + 1) / 4);
                     let irr;
                     if (tvpi <= 0.1) {
                         irr = -50; // Cap downside
                     } else if (tvpi <= 0.8) {
                         irr = -20 + (tvpi - 0.1) * 20; // Scale from -20% to 0%
                     } else {
                         // For TVPI > 0.8, calculate based on realistic annualized returns
                         const annualizedReturn = Math.pow(tvpi, 1/years) - 1;
                         irr = Math.min(50, Math.max(-50, annualizedReturn * 100));
                     }
                     
                     tvpiData.push(Math.max(0.1, Math.min(5.0, tvpi)));
                     irrData.push(Math.max(-50, Math.min(50, irr)));
                 }
             }

                         // TVPI Chart
             const tvpiCtx = document.getElementById('tvpiChart').getContext('2d');
             if (tvpiChart) tvpiChart.destroy();
             tvpiChart = new Chart(tvpiCtx, {
                 type: 'line',
                 data: {
                     labels: labels,
                     datasets: [{
                         label: 'Net TVPI',
                         data: tvpiData,
                         borderColor: '#1a73e8',
                         backgroundColor: 'rgba(26, 115, 232, 0.1)',
                         tension: 0.4,
                         fill: true
                     }]
                 },
                 options: {
                     responsive: true,
                     maintainAspectRatio: false,
                     animation: false,
                     plugins: {
                         title: {
                             display: true,
                             text: `Current Net TVPI: ${performance.netTVPI.toFixed(2)}x`
                         }
                     },
                     scales: {
                         y: {
                             beginAtZero: false,
                             min: 0,
                             max: Math.max(2.0, Math.max(...tvpiData) * 1.2),
                             title: { display: true, text: 'Net TVPI Multiple' },
                             ticks: {
                                 callback: function(value) {
                                     return value.toFixed(1) + 'x';
                                 }
                             }
                         }
                     }
                 }
             });

                         // IRR Chart
             const irrCtx = document.getElementById('irrChart').getContext('2d');
             if (irrChart) irrChart.destroy();
             irrChart = new Chart(irrCtx, {
                 type: 'line',
                 data: {
                     labels: labels,
                     datasets: [{
                         label: 'Net IRR',
                         data: irrData,
                         borderColor: '#34a853',
                         backgroundColor: 'rgba(52, 168, 83, 0.1)',
                         tension: 0.4,
                         fill: true
                     }]
                 },
                 options: {
                     responsive: true,
                     maintainAspectRatio: false,
                     animation: false,
                     plugins: {
                         title: {
                             display: true,
                             text: `Current Net IRR: ${(performance.currentIRR * 100).toFixed(1)}%`
                         }
                     },
                     scales: {
                         y: {
                             min: Math.min(-20, Math.min(...irrData) * 1.2),
                             max: Math.max(20, Math.max(...irrData) * 1.2),
                             title: { display: true, text: 'Net IRR (%)' },
                             ticks: {
                                 callback: function(value) {
                                     return value.toFixed(1) + '%';
                                 }
                             }
                         }
                     }
                 }
             });
        }

        function setupScenarioCards() {
            document.querySelectorAll('.scenario-card').forEach(card => {
                card.addEventListener('click', function() {
                    document.querySelectorAll('.scenario-card').forEach(c => {
                        c.classList.remove('selected');
                        c.style.background = 'white';
                        c.style.borderColor = '#ddd';
                    });
                    this.classList.add('selected');
                    selectedScenario = this.dataset.scenario;
                });
            });
        }

        function runMonteCarloAnalysis() {
            const button = event.target;
            button.textContent = '🎲 Running Simulations...';
            button.disabled = true;

            setTimeout(() => {
                const simResults = generateScenarioSimulations();
                generateForecastCharts(simResults);
                generateDistributionChart(simResults);
                generateScenarioBreakdown(simResults);

                button.textContent = '🎲 Run Monte Carlo Analysis (100,000 simulations)';
                button.disabled = false;
            }, 100);
        }

                          // Exit return parameters by entry stage (full investment lifecycle)
         // These represent the TOTAL return from investment to exit, not stage-to-stage returns
         // Parameters: [failureRate, logNormalMean, logNormalStd, paretoAlpha, paretoXm, tailProb]
         const EXIT_RETURN_PARAMS = {
             "Seed": [0.65, 0.70, 1.00, 2.3, 10.0, 0.15],      // High risk, high reward
             "SeriesA": [0.35, 0.90, 0.80, 2.5, 10.0, 0.10],   // Moderate risk
             "SeriesB": [0.20, 1.10, 0.60, 2.8, 10.0, 0.06],   // Lower risk
             "SeriesC": [0.10, 1.00, 0.60, 2.5, 8.0, 0.08],    // Even lower risk
             "Growth": [0.05, 0.95, 0.45, 2.8, 7.0, 0.05]      // Lowest risk
         };

         // Stage progression mapping (for reference, but not used in new model)
         const STAGE_PROGRESSION = {
             "Seed": "SeriesA",
             "SeriesA": "SeriesB",
             "SeriesB": "SeriesC",
             "SeriesC": "Growth",
             "Growth": "Growth"
         };

         // Draw FINAL exit return based on entry stage (replaces compounding model)
         function drawExitReturn(entryStage) {
             const params = EXIT_RETURN_PARAMS[entryStage];
             const [failureRate, logNormalMean, logNormalStd, paretoAlpha, paretoXm, tailProb] = params;

             // Check for failure
             if (Math.random() < failureRate) {
                 return Math.random() * 0.3; // 0-30% recovery
             }

             // Mixture model: log-normal + Pareto tail
             if (Math.random() < tailProb) {
                 // Pareto tail for extreme outcomes
                 const u = Math.random();
                 return paretoXm / Math.pow(1 - u, 1 / paretoAlpha);
             } else {
                 // Log-normal for typical outcomes
                 const normal = Math.sqrt(-2 * Math.log(Math.random())) * Math.cos(2 * Math.PI * Math.random());
                 return Math.exp(logNormalMean + logNormalStd * normal);
             }
         }

         function generateScenarioSimulations() {
             const performance = calculateCurrentPerformance();
             const NUM_SIMULATIONS = 100000;

             const scenarios = {
                 stop: { newDeploymentRate: 0.0, followOnRate: 0.8 },
                 conservative: { newDeploymentRate: 0.5, followOnRate: 0.5 },
                 aggressive: { newDeploymentRate: 0.8, followOnRate: 0.2 },
                 followon: { newDeploymentRate: 0.3, followOnRate: 0.7 }
             };

             const results = {};

             // 🎯 FIXED: Generate base outcomes ONCE for existing companies (same across all scenarios)
             function generateBaseOutcomesForFullSim() {
                 const baseOutcomes = {};

                 Object.keys(portfolioData).forEach(companyName => {
                     const company = portfolioData[companyName];
                     const companyInvested = company.investments.reduce((sum, inv) => sum + inv.amount, 0);
                     const latestMark = company.quarterlyMarks.length > 0 ?
                         company.quarterlyMarks[company.quarterlyMarks.length - 1] : null;
                     const currentValue = latestMark ? latestMark.carryingValue : companyInvested;
                     const currentMultiple = companyInvested > 0 ? currentValue / companyInvested : 0;

                     // Determine entry stage
                     const firstInvestment = company.investments[0];
                     const entryStage = firstInvestment.round.replace(' ', '');

                     let baseOutcome = {
                         entryStage: entryStage,
                         currentMultiple: currentMultiple,
                         companyInvested: companyInvested,
                         currentValue: currentValue,
                         exitReturn: 0,
                         followOnSuccess: false,
                         followOnReturn: 0,
                         isEligibleForFollowOn: currentMultiple >= 1.5
                     };

                     if (currentValue === 0) {
                         // Dead company stays dead
                         baseOutcome.exitReturn = 0;
                         baseOutcome.followOnSuccess = false;
                         baseOutcome.followOnReturn = 0;
                         baseOutcome.isEligibleForFollowOn = false;
                     } else {
                         // Draw SINGLE exit return (same for all scenarios)
                         baseOutcome.exitReturn = drawExitReturn(entryStage);

                         // Determine follow-on success/failure (same for all scenarios)
                         if (baseOutcome.isEligibleForFollowOn) {
                             baseOutcome.followOnSuccess = Math.random() < 0.75; // 75% success rate
                             if (baseOutcome.followOnSuccess) {
                                 baseOutcome.followOnReturn = 2.0 + Math.random() * 4.0; // 2x-6x
                             } else {
                                 baseOutcome.followOnReturn = Math.random() * 0.5; // 0-0.5x failure
                             }
                         }
                     }

                     baseOutcomes[companyName] = baseOutcome;
                 });

                 return baseOutcomes;
             }

             // Generate base outcomes once for all scenarios
             const baseOutcomes = generateBaseOutcomesForFullSim();

             Object.keys(scenarios).forEach(scenarioType => {
                 const scenario = scenarios[scenarioType];
                 const scenarioResults = [];

                 for (let sim = 0; sim < NUM_SIMULATIONS; sim++) {
                     let totalValue = performance.totalCurrentValue;
                     let totalCalled = performance.calledCapital;

                     // Deploy remaining capital according to scenario
                     const remainingCapital = performance.remainingCapital;
                     const newDeployment = remainingCapital * scenario.newDeploymentRate;
                     const followOnReserve = remainingCapital * scenario.followOnRate;
                     
                     // 🎯 FIXED: Use SAME base outcomes for existing companies across all scenarios
                     Object.keys(baseOutcomes).forEach(companyName => {
                         const baseOutcome = baseOutcomes[companyName];

                         if (baseOutcome.currentValue === 0) {
                             // Dead company stays dead
                             totalValue = totalValue - baseOutcome.currentValue + 0;
                         } else {
                             // Use SAME exit return across all scenarios
                             const finalValue = baseOutcome.companyInvested * baseOutcome.exitReturn;
                             totalValue = totalValue - baseOutcome.currentValue + finalValue;
                         }

                        // Follow-on opportunities using SAME success/failure outcomes
                        if (followOnReserve > 0 && baseOutcome.isEligibleForFollowOn && baseOutcome.followOnSuccess) {
                            // Count eligible companies that will succeed (same across all scenarios)
                            const eligibleSuccessfulCompanies = Object.values(baseOutcomes).filter(bo =>
                                bo.isEligibleForFollowOn && bo.followOnSuccess
                            ).length;

                            if (eligibleSuccessfulCompanies > 0) {
                                // Randomize follow-on amount (0.2x to 1.0x of original investment)
                                const followOnMultiplier = 0.2 + Math.random() * 0.8;
                                const maxFollowOn = baseOutcome.companyInvested * followOnMultiplier;

                                // Distribute follow-on reserve among eligible successful companies
                                const followOnAmount = Math.min(maxFollowOn, followOnReserve / eligibleSuccessfulCompanies);

                                // Use SAME follow-on return across all scenarios
                                totalValue += followOnAmount * baseOutcome.followOnReturn;
                                totalCalled += followOnAmount;
                            }
                        }
                    });
                    
                                         // New investments using proper stage distributions
                     if (newDeployment > 0) {
                         const avgCheckSize = 20e6;
                         const numNewDeals = Math.floor(newDeployment / avgCheckSize);
                         
                         for (let deal = 0; deal < numNewDeals; deal++) {
                             // New investments at Series B stage - use single exit return model
                             const entryStage = 'SeriesB';

                             // Draw a single exit return based on Series B entry stage
                             const exitMultiple = drawExitReturn(entryStage);

                             // Add final value
                             totalValue += avgCheckSize * exitMultiple;
                             totalCalled += avgCheckSize;
                         }
                     }
                    
                    // Add remaining management fees (5 more years)
                    const additionalMgmtFees = performance.fundSize * performance.managementFeeRate * 5;
                    totalCalled += additionalMgmtFees;
                    
                    // Calculate final net TVPI
                    const grossReturn = totalValue;
                    const profits = Math.max(0, grossReturn - totalCalled);
                    const carry = profits * performance.carryRate;
                    const netReturn = grossReturn - carry;
                    const netTVPI = totalCalled > 0 ? netReturn / totalCalled : 0;
                    
                    // Calculate IRR
                    const finalYears = performance.yearsElapsed + 5;
                    const finalIRR = netTVPI > 0 ? Math.pow(netTVPI, 1/finalYears) - 1 : -0.5;
                    
                    scenarioResults.push({
                        netTVPI,
                        finalIRR,
                        totalCalled,
                        netReturn
                    });
                }
                
                // Calculate statistics
                scenarioResults.sort((a, b) => a.netTVPI - b.netTVPI);
                const mean = scenarioResults.reduce((sum, r) => sum + r.netTVPI, 0) / NUM_SIMULATIONS;
                const median = scenarioResults[Math.floor(NUM_SIMULATIONS / 2)].netTVPI;
                const p25 = scenarioResults[Math.floor(NUM_SIMULATIONS * 0.25)].netTVPI;
                const p75 = scenarioResults[Math.floor(NUM_SIMULATIONS * 0.75)].netTVPI;
                const meanIRR = scenarioResults.reduce((sum, r) => sum + r.finalIRR, 0) / NUM_SIMULATIONS;
                
                results[scenarioType] = {
                    mean,
                    median,
                    p25,
                    p75,
                    meanIRR,
                    scenario: scenario,
                    results: scenarioResults
                };
            });
            
            return results;
        }

        function generateForecastCharts(simResults) {
            const performance = calculateCurrentPerformance();
            
            // Generate forecast data
            const scenarios = {
                stop: { label: 'Stop Investing', color: '#ea4335' },
                conservative: { label: 'Conservative', color: '#fbbc04' },
                aggressive: { label: 'Aggressive', color: '#34a853' },
                followon: { label: 'Follow-On Focused', color: '#1a73e8' }
            };
            
            const tvpiDatasets = [];
            const irrDatasets = [];
            const labels = ['Current', '1 Year', '2 Years', '3 Years', '4 Years', '5 Years'];
            
            Object.keys(scenarios).forEach(scenarioType => {
                const scenario = scenarios[scenarioType];
                const result = simResults[scenarioType];
                
                // Create progression from current to final
                const tvpiProgression = [
                    performance.netTVPI,
                    performance.netTVPI + (result.mean - performance.netTVPI) * 0.2,
                    performance.netTVPI + (result.mean - performance.netTVPI) * 0.4,
                    performance.netTVPI + (result.mean - performance.netTVPI) * 0.6,
                    performance.netTVPI + (result.mean - performance.netTVPI) * 0.8,
                    result.mean
                ];
                
                const irrProgression = [
                    performance.currentIRR * 100,
                    (performance.currentIRR + (result.meanIRR - performance.currentIRR) * 0.2) * 100,
                    (performance.currentIRR + (result.meanIRR - performance.currentIRR) * 0.4) * 100,
                    (performance.currentIRR + (result.meanIRR - performance.currentIRR) * 0.6) * 100,
                    (performance.currentIRR + (result.meanIRR - performance.currentIRR) * 0.8) * 100,
                    result.meanIRR * 100
                ];
                
                tvpiDatasets.push({
                    label: scenario.label,
                    data: tvpiProgression,
                    borderColor: scenario.color,
                    backgroundColor: scenario.color + '20',
                    tension: 0.4,
                    fill: false
                });
                
                irrDatasets.push({
                    label: scenario.label,
                    data: irrProgression,
                    borderColor: scenario.color,
                    backgroundColor: scenario.color + '20',
                    tension: 0.4,
                    fill: false
                });
            });
            
                         // TVPI Forecast Chart
             const forecastTVPICtx = document.getElementById('forecastTVPIChart').getContext('2d');
             if (forecastTVPIChart) forecastTVPIChart.destroy();
             
             // Find max value across all datasets for consistent scaling
             const maxTVPI = Math.max(...tvpiDatasets.flatMap(d => d.data));
             
             forecastTVPIChart = new Chart(forecastTVPICtx, {
                 type: 'line',
                 data: { labels, datasets: tvpiDatasets },
                 options: {
                     responsive: true,
                     maintainAspectRatio: false,
                     animation: false,
                     plugins: {
                         title: { display: true, text: 'Net TVPI Forecast by Strategy' }
                     },
                     scales: {
                         y: { 
                             beginAtZero: false,
                             min: 0,
                             max: Math.max(3.0, maxTVPI * 1.1),
                             title: { display: true, text: 'Net TVPI Multiple' },
                             ticks: { callback: value => value.toFixed(1) + 'x' }
                         }
                     }
                 }
             });
            
                         // IRR Forecast Chart
             const forecastIRRCtx = document.getElementById('forecastIRRChart').getContext('2d');
             if (forecastIRRChart) forecastIRRChart.destroy();
             
             // Find min/max values across all datasets for consistent scaling
             const allIRRValues = irrDatasets.flatMap(d => d.data);
             const minIRR = Math.min(...allIRRValues);
             const maxIRR = Math.max(...allIRRValues);
             
             forecastIRRChart = new Chart(forecastIRRCtx, {
                 type: 'line',
                 data: { labels, datasets: irrDatasets },
                 options: {
                     responsive: true,
                     maintainAspectRatio: false,
                     animation: false,
                     plugins: {
                         title: { display: true, text: 'Net IRR Forecast by Strategy' }
                     },
                     scales: {
                         y: {
                             min: Math.min(-10, minIRR * 1.2),
                             max: Math.max(30, maxIRR * 1.1),
                             title: { display: true, text: 'Net IRR (%)' },
                             ticks: { callback: value => value.toFixed(1) + '%' }
                         }
                     }
                 }
             });
        }

        function generateDistributionChart(simResults) {
            try {
                console.log('Starting distribution chart generation...');

                // Create histogram data for each scenario
                const scenarios = {
                    stop: { label: 'Stop Investing', color: '#ea4335' },
                    conservative: { label: 'Conservative', color: '#fbbc04' },
                    aggressive: { label: 'Aggressive', color: '#34a853' },
                    followon: { label: 'Follow-On Focused', color: '#1a73e8' }
                };

                // Simplified approach: use fewer bins and sample data if needed
                const numBins = 30; // Reduced from 50
                const datasets = [];

                // Find overall range for consistent binning
                let minVal = Infinity;
                let maxVal = -Infinity;

                Object.keys(scenarios).forEach(scenarioType => {
                    if (simResults[scenarioType] && simResults[scenarioType].results) {
                        const values = simResults[scenarioType].results.map(r => r.netTVPI);
                        minVal = Math.min(minVal, ...values);
                        maxVal = Math.max(maxVal, ...values);
                    }
                });

                console.log(`Range: ${minVal.toFixed(2)} to ${maxVal.toFixed(2)}`);

                const binWidth = (maxVal - minVal) / numBins;

                Object.keys(scenarios).forEach(scenarioType => {
                    const scenario = scenarios[scenarioType];

                    if (!simResults[scenarioType] || !simResults[scenarioType].results) {
                        console.warn(`No results for scenario: ${scenarioType}`);
                        return;
                    }

                    const values = simResults[scenarioType].results.map(r => r.netTVPI);
                    console.log(`Processing ${scenarioType}: ${values.length} values`);

                    // Create histogram with simplified logic
                    const histogram = new Array(numBins).fill(0);

                    values.forEach(value => {
                        if (value >= minVal && value <= maxVal) {
                            let binIndex = Math.floor((value - minVal) / binWidth);
                            binIndex = Math.max(0, Math.min(binIndex, numBins - 1));
                            histogram[binIndex]++;
                        }
                    });

                    // Convert to percentage (simpler than density)
                    const percentages = histogram.map(count => (count / values.length) * 100);

                    // Create data points for line chart
                    const data = [];
                    for (let i = 0; i < numBins; i++) {
                        data.push({
                            x: minVal + (i + 0.5) * binWidth, // Center of bin
                            y: percentages[i]
                        });
                    }

                    datasets.push({
                        label: scenario.label,
                        data: data,
                        borderColor: scenario.color,
                        backgroundColor: scenario.color + '20',
                        fill: false,
                        tension: 0.3,
                        pointRadius: 0,
                        borderWidth: 2
                    });
                });

                console.log(`Created ${datasets.length} datasets`);

                // Create chart
                const distributionCtx = document.getElementById('distributionChart').getContext('2d');
                if (distributionChart) distributionChart.destroy();

                distributionChart = new Chart(distributionCtx, {
                    type: 'line',
                    data: { datasets: datasets },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Net TVPI Distribution by Strategy'
                            },
                            legend: {
                                display: true,
                                position: 'top'
                            }
                        },
                        scales: {
                            x: {
                                type: 'linear',
                                title: { display: true, text: 'Net TVPI Multiple' },
                                ticks: {
                                    callback: value => value.toFixed(1) + 'x'
                                }
                            },
                            y: {
                                title: { display: true, text: 'Percentage of Outcomes (%)' },
                                beginAtZero: true
                            }
                        }
                    }
                });

                console.log('Distribution chart created successfully');

            } catch (error) {
                console.error('Error creating distribution chart:', error);
                // Hide the chart container if there's an error
                document.getElementById('distributionChart').style.display = 'none';
            }
        }

        // Enhanced diagnostic function with follow-on details and median outcomes
        function runDiagnosticSimulation() {
            const performance = calculateCurrentPerformance();
            const results = {
                currentPortfolio: {},
                medianOutcomes: {}
            };

            // Capture current portfolio state
            Object.keys(portfolioData).forEach(companyName => {
                const company = portfolioData[companyName];
                const invested = company.investments.reduce((sum, inv) => sum + inv.amount, 0);
                const latestMark = company.quarterlyMarks.length > 0 ?
                    company.quarterlyMarks[company.quarterlyMarks.length - 1] : null;
                const currentValue = latestMark ? latestMark.carryingValue : invested;
                const currentMultiple = invested > 0 ? currentValue / invested : 0;

                results.currentPortfolio[companyName] = {
                    invested: invested / 1e6,
                    currentValue: currentValue / 1e6,
                    currentMultiple: currentMultiple
                };
            });

            // Run median simulations for each scenario to get representative outcomes
            const scenarios = {
                stop: { newDeploymentRate: 0.0, followOnRate: 0.8 },
                conservative: { newDeploymentRate: 0.5, followOnRate: 0.5 },
                aggressive: { newDeploymentRate: 0.8, followOnRate: 0.2 },
                followon: { newDeploymentRate: 0.3, followOnRate: 0.7 }
            };

            // 🎯 FIXED: Generate base outcomes ONCE for existing companies (same across all scenarios)
            function generateBaseOutcomes() {
                const baseOutcomes = {};

                Object.keys(portfolioData).forEach(companyName => {
                    const company = portfolioData[companyName];
                    const companyInvested = company.investments.reduce((sum, inv) => sum + inv.amount, 0);
                    const latestMark = company.quarterlyMarks.length > 0 ?
                        company.quarterlyMarks[company.quarterlyMarks.length - 1] : null;
                    const currentValue = latestMark ? latestMark.carryingValue : companyInvested;
                    const currentMultiple = companyInvested > 0 ? currentValue / companyInvested : 0;

                    // Determine entry stage
                    const firstInvestment = company.investments[0];
                    const entryStage = firstInvestment.round.replace(' ', '');

                    let baseOutcome = {
                        entryStage: entryStage,
                        currentMultiple: currentMultiple,
                        companyInvested: companyInvested,
                        currentValue: currentValue,
                        exitReturn: 0,
                        followOnSuccess: false,
                        followOnReturn: 0,
                        isEligibleForFollowOn: currentMultiple >= 1.5
                    };

                    if (currentValue === 0) {
                        // Dead company stays dead
                        baseOutcome.exitReturn = 0;
                        baseOutcome.followOnSuccess = false;
                        baseOutcome.followOnReturn = 0;
                        baseOutcome.isEligibleForFollowOn = false;
                    } else {
                        // Draw SINGLE exit return (same for all scenarios)
                        baseOutcome.exitReturn = drawExitReturn(entryStage);

                        // Determine follow-on success/failure (same for all scenarios)
                        if (baseOutcome.isEligibleForFollowOn) {
                            baseOutcome.followOnSuccess = Math.random() < 0.75; // 75% success rate
                            if (baseOutcome.followOnSuccess) {
                                baseOutcome.followOnReturn = 2.0 + Math.random() * 4.0; // 2x-6x
                            } else {
                                baseOutcome.followOnReturn = Math.random() * 0.5; // 0-0.5x failure
                            }
                        }
                    }

                    baseOutcomes[companyName] = baseOutcome;
                });

                return baseOutcomes;
            }

            // Generate base outcomes once
            const baseOutcomes = generateBaseOutcomes();

            // Run 100 simulations per scenario to find median outcomes
            Object.keys(scenarios).forEach(scenarioType => {
                const scenario = scenarios[scenarioType];
                const simResults = [];

                for (let sim = 0; sim < 100; sim++) {
                    let totalValue = performance.totalCurrentValue;
                    let companyOutcomes = {};
                    const remainingCapital = performance.remainingCapital;
                    const followOnReserve = remainingCapital * scenario.followOnRate;

                    // 🎯 FIXED: Use SAME base outcomes for existing companies across all scenarios
                    Object.keys(baseOutcomes).forEach(companyName => {
                        const baseOutcome = baseOutcomes[companyName];

                        let outcome = {
                            entryStage: baseOutcome.entryStage,
                            currentMultiple: baseOutcome.currentMultiple,
                            exitReturn: baseOutcome.exitReturn, // SAME exit return across all scenarios
                            followOnAmount: 0,
                            followOnReturn: baseOutcome.followOnReturn, // SAME follow-on return across all scenarios
                            totalReturn: 0,
                            finalValue: 0,
                            isNewInvestment: false
                        };

                        if (baseOutcome.currentValue === 0) {
                            // Dead company
                            outcome.exitReturn = 0;
                            outcome.totalReturn = 0;
                            outcome.finalValue = 0;
                        } else {
                            // Calculate follow-on amount based on scenario's allocation
                            let followOnAmount = 0;

                            if (followOnReserve > 0 && baseOutcome.isEligibleForFollowOn && baseOutcome.followOnSuccess) {
                                // Count eligible companies that will succeed
                                const eligibleSuccessfulCompanies = Object.values(baseOutcomes).filter(bo =>
                                    bo.isEligibleForFollowOn && bo.followOnSuccess
                                ).length;

                                if (eligibleSuccessfulCompanies > 0) {
                                    // Randomize follow-on amount (0.2x to 1.0x of original investment)
                                    const followOnMultiplier = 0.2 + Math.random() * 0.8;
                                    const maxFollowOn = baseOutcome.companyInvested * followOnMultiplier;

                                    // Distribute follow-on reserve among eligible successful companies
                                    followOnAmount = Math.min(maxFollowOn, followOnReserve / eligibleSuccessfulCompanies);
                                }
                            }

                            outcome.followOnAmount = followOnAmount / 1e6; // Convert to millions

                            // Calculate total return (base investment + follow-on)
                            const baseValue = baseOutcome.companyInvested * baseOutcome.exitReturn;
                            const followOnValue = followOnAmount * baseOutcome.followOnReturn;
                            const totalValue = baseValue + followOnValue;
                            const totalInvested = baseOutcome.companyInvested + followOnAmount;

                            outcome.totalReturn = totalInvested > 0 ? totalValue / totalInvested : 0;
                            outcome.finalValue = totalValue / 1e6;
                        }

                        companyOutcomes[companyName] = outcome;
                    });

                    // Add new investments based on scenario
                    const newDeployment = remainingCapital * scenario.newDeploymentRate;
                    if (newDeployment > 0) {
                        const avgCheckSize = 20e6; // $20M per new investment
                        const numNewDeals = Math.floor(newDeployment / avgCheckSize);

                        for (let deal = 1; deal <= numNewDeals; deal++) {
                            const companyName = `Company ${deal}`;
                            const entryStage = 'SeriesB';

                            // Draw exit return for new investment
                            const exitMultiple = drawExitReturn(entryStage);

                            const outcome = {
                                entryStage: entryStage,
                                currentMultiple: 1.0, // New investments start at 1.0x
                                exitReturn: exitMultiple,
                                followOnAmount: 0, // New investments don't get follow-ons in first simulation
                                followOnReturn: 0,
                                totalReturn: exitMultiple,
                                finalValue: (avgCheckSize * exitMultiple) / 1e6,
                                isNewInvestment: true
                            };

                            companyOutcomes[companyName] = outcome;
                        }
                    }

                    simResults.push(companyOutcomes);
                }

                // Find median simulation for this scenario
                const medianIndex = Math.floor(simResults.length / 2);
                results.medianOutcomes[scenarioType] = simResults[medianIndex];
            });

            return results;
        }

        function generateScenarioBreakdown(simResults) {
            const scenarios = {
                stop: { name: 'Stop Investing', class: 'stop' },
                conservative: { name: 'Conservative', class: 'conservative' },
                aggressive: { name: 'Aggressive', class: 'aggressive' },
                followon: { name: 'Follow-On Focused', class: 'followon' }
            };
            
            // First, let's add a diagnostic run to see what's happening
            const diagnosticResults = runDiagnosticSimulation();
            
            let tableHTML = `
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <thead>
                        <tr style="background-color: #f5f7fa;">
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">Strategy</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">Mean Net TVPI</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">Median Net TVPI</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">P25-P75 Range</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">Mean Net IRR</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">P(Net TVPI ≥ 2x)</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">P(Net TVPI ≥ 3x)</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            Object.keys(scenarios).forEach(scenarioType => {
                const scenario = scenarios[scenarioType];
                const result = simResults[scenarioType];
                const prob2x = result.results.filter(r => r.netTVPI >= 2.0).length / result.results.length;
                const prob3x = result.results.filter(r => r.netTVPI >= 3.0).length / result.results.length;

                tableHTML += `
                    <tr>
                        <td style="padding: 12px; border: 1px solid #ddd;"><strong>${scenario.name}</strong></td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right;">${result.mean.toFixed(2)}x</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right;">${result.median.toFixed(2)}x</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right;">${result.p25.toFixed(2)}x - ${result.p75.toFixed(2)}x</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right;">${(result.meanIRR * 100).toFixed(1)}%</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right;">${(prob2x * 100).toFixed(1)}%</td>
                        <td style="padding: 12px; border: 1px solid #ddd; text-align: right;">${(prob3x * 100).toFixed(1)}%</td>
                    </tr>
                `;
            });
            
                         tableHTML += `
                     </tbody>
                 </table>
             `;
            
            // Add enhanced diagnostic table showing all scenarios
            tableHTML += `
                <h4 style="margin-top: 30px; margin-bottom: 15px;">🔍 Diagnostic: Median Portfolio Outcomes by Strategy</h4>
                <div style="margin-bottom: 20px; padding: 12px; background: #e3f2fd; border-radius: 6px; font-size: 0.9em;">
                    <strong>What this shows:</strong> Median outcomes from 100 simulations per strategy, showing how follow-on amounts and returns vary by scenario.
                </div>
            `;

            // Create tabs for each scenario
            const scenarioNames = {
                stop: '🛑 Stop Investing',
                conservative: '⚖️ Conservative',
                aggressive: '🚀 Aggressive',
                followon: '💎 Follow-On Focused'
            };

            Object.keys(scenarioNames).forEach((scenarioType, index) => {
                const scenarioName = scenarioNames[scenarioType];
                const outcomes = diagnosticResults.medianOutcomes[scenarioType];

                tableHTML += `
                    <div style="margin: 20px 0;">
                        <h5 style="margin: 15px 0 10px 0; color: #1565c0;">${scenarioName}</h5>
                        <table style="width: 100%; border-collapse: collapse; font-size: 0.85em;">
                            <thead>
                                <tr style="background-color: #f5f7fa;">
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Company</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Invested</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Current</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Entry Stage</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Exit Return</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Follow-On $</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Follow-On Return</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Total Return</th>
                                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Final Value</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                // Separate existing and new companies
                const existingCompanies = [];
                const newCompanies = [];

                Object.entries(outcomes).forEach(([company, outcome]) => {
                    if (outcome.isNewInvestment) {
                        newCompanies.push([company, outcome]);
                    } else {
                        existingCompanies.push([company, outcome]);
                    }
                });

                // Add existing companies first
                existingCompanies.forEach(([company, outcome]) => {
                    const current = diagnosticResults.currentPortfolio[company];
                    const followOnDisplay = outcome.followOnAmount > 0 ? `$${outcome.followOnAmount.toFixed(1)}M` : '-';
                    const followOnReturnDisplay = outcome.followOnReturn > 0 ? `${outcome.followOnReturn.toFixed(2)}x` : '-';

                    tableHTML += `
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>${company}</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${current.invested.toFixed(1)}M</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${current.currentMultiple.toFixed(2)}x</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${outcome.entryStage}</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${outcome.exitReturn.toFixed(2)}x</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${followOnDisplay}</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${followOnReturnDisplay}</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${outcome.totalReturn.toFixed(2)}x</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${outcome.finalValue.toFixed(1)}M</td>
                        </tr>
                    `;
                });

                // Add separator and new companies if any
                if (newCompanies.length > 0) {
                    tableHTML += `
                        <tr style="background-color: #e8f5e8;">
                            <td colspan="9" style="padding: 8px; border: 1px solid #ddd; text-align: center; font-weight: bold; color: #2e7d32;">
                                🆕 NEW INVESTMENTS (${newCompanies.length} companies @ $20M each)
                            </td>
                        </tr>
                    `;

                    newCompanies.forEach(([company, outcome]) => {
                        tableHTML += `
                            <tr style="background-color: #f1f8e9;">
                                <td style="padding: 8px; border: 1px solid #ddd;"><strong>${company}</strong> <span style="color: #4caf50;">NEW</span></td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$20.0M</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">1.00x</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${outcome.entryStage}</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${outcome.exitReturn.toFixed(2)}x</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">-</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">-</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${outcome.totalReturn.toFixed(2)}x</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${outcome.finalValue.toFixed(1)}M</td>
                            </tr>
                        `;
                    });
                }

                tableHTML += `
                            </tbody>
                        </table>
                    </div>
                `;
            });
            
            tableHTML += `
                <div style="margin-top: 20px; padding: 16px; background: #f8f9fa; border-radius: 8px; font-size: 0.875em; color: #5f6368;">
                    <strong>Fixed Monte Carlo Methodology:</strong> Each scenario runs 100,000 simulations over 5 years.<br>
                    • <strong>Identical Path Dependency:</strong> All scenarios use the same exit returns for existing portfolio companies<br>
                    • <strong>Only Capital Allocation Differs:</strong> Stop (0% new), Conservative (50% new), Aggressive (80% new), Follow-On (30% new)<br>
                    • <strong>New Investments:</strong> Added as "Company 1, 2, 3..." at Series B stage ($20M each)<br>
                    • <strong>Follow-On Logic:</strong> Randomized amounts (0.2x-1.0x), 75% success rate (2x-6x returns)<br>
                    • <strong>Trade-Off Visible:</strong> More new investments = less follow-on capital per winner<br>
                    • <strong>Fees Included:</strong> Management fees (${document.getElementById('managementFee').value}% annually) and carry (${document.getElementById('carriedInterest').value}%)<br>
                    <br><strong>🎯 Key Insight:</strong> Scenarios now properly show diversification vs. concentration trade-offs!
                </div>
             `;
            
            document.getElementById('scenarioBreakdown').innerHTML = tableHTML;
        }

        // Initialize with sample data
        document.addEventListener('DOMContentLoaded', function() {
            loadSampleData();
        });
    </script>
</body>
</html> 