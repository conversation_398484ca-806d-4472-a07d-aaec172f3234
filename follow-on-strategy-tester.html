<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Follow-On Strategy Tester</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .subtitle {
            text-align: center;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
            font-size: 1.1em;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }

        .strategy-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .strategy-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
        }

        .strategy-card.active {
            border-color: #3498db;
            background: #e8f4fd;
        }

        .strategy-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .strategy-logic {
            font-size: 0.9em;
            color: #7f8c8d;
            line-height: 1.5;
        }

        .sizing-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .sizing-table th,
        .sizing-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 0.9em;
        }

        .sizing-table th {
            background-color: #f1f3f4;
            font-weight: 600;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        input, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #3498db;
        }

        button {
            padding: 14px 24px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }

        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .result-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .result-card.winner {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        .metric-value {
            font-size: 2em;
            font-weight: 700;
            color: #3498db;
            margin: 10px 0;
        }

        .metric-value.winner {
            color: #27ae60;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .chart-container {
            height: 400px;
            margin-top: 20px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }

        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .comparison-table .best {
            background-color: #d5f4e6;
            font-weight: 600;
            color: #27ae60;
        }

        .results {
            display: none;
        }

        .results.show {
            display: block;
        }

        @media (max-width: 768px) {
            .strategy-grid {
                grid-template-columns: 1fr;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Follow-On Strategy Tester</h1>
        <p class="subtitle">Compare different follow-on sizing strategies to optimize portfolio returns</p>

        <!-- Strategy Configuration -->
        <div class="card">
            <h2>Follow-On Sizing Strategies</h2>
            <div class="strategy-grid">
                <!-- Strategy 1: User's Intuitive Approach -->
                <div class="strategy-card active" onclick="selectStrategy(1)">
                    <div class="strategy-title">📈 Intuitive Strategy (Your Approach)</div>
                    <div class="strategy-logic">
                        <strong>Logic:</strong> More capital early, less capital later<br>
                        <strong>Goal:</strong> Maximize early-stage upside, manage late-stage risk
                    </div>
                    <table class="sizing-table">
                        <tr><th>Stage Transition</th><th>Follow-On Size</th></tr>
                        <tr><td>Seed → Series A</td><td>2.5x Seed amount</td></tr>
                        <tr><td>Series A → Series B</td><td>1.0x Series A amount</td></tr>
                        <tr><td>Series B → Series C</td><td>0.7x Series B amount</td></tr>
                        <tr><td>Series C → Growth</td><td>0.5x Series C amount</td></tr>
                    </table>
                </div>

                <!-- Strategy 2: Current Model -->
                <div class="strategy-card" onclick="selectStrategy(2)">
                    <div class="strategy-title">🤖 Current Model</div>
                    <div class="strategy-logic">
                        <strong>Logic:</strong> Fixed multipliers by initial stage<br>
                        <strong>Goal:</strong> Simple consistent approach
                    </div>
                    <table class="sizing-table">
                        <tr><th>Initial Stage</th><th>Follow-On Size</th></tr>
                        <tr><td>Seed</td><td>2.0x initial amount</td></tr>
                        <tr><td>Series A</td><td>1.6x initial amount</td></tr>
                        <tr><td>Series B</td><td>1.4x initial amount</td></tr>
                        <tr><td>Series C</td><td>1.2x initial amount</td></tr>
                        <tr><td>Growth</td><td>0.8x initial amount</td></tr>
                    </table>
                </div>

                <!-- Strategy 3: Conservative Approach -->
                <div class="strategy-card" onclick="selectStrategy(3)">
                    <div class="strategy-title">🛡️ Conservative Strategy</div>
                    <div class="strategy-logic">
                        <strong>Logic:</strong> Always match initial investment<br>
                        <strong>Goal:</strong> Maintain ownership without over-concentration
                    </div>
                    <table class="sizing-table">
                        <tr><th>All Transitions</th><th>Follow-On Size</th></tr>
                        <tr><td>Any → Next Stage</td><td>1.0x initial amount</td></tr>
                    </table>
                </div>

                <!-- Strategy 4: Aggressive Winner Doubling -->
                <div class="strategy-card" onclick="selectStrategy(4)">
                    <div class="strategy-title">🚀 Aggressive Winner Strategy</div>
                    <div class="strategy-logic">
                        <strong>Logic:</strong> Double down on high performers<br>
                        <strong>Goal:</strong> Maximize concentration in winners
                    </div>
                    <table class="sizing-table">
                        <tr><th>Performance Tier</th><th>Follow-On Size</th></tr>
                        <tr><td>TVPI ≥ 3x</td><td>3.0x initial amount</td></tr>
                        <tr><td>TVPI 2-3x</td><td>2.0x initial amount</td></tr>
                        <tr><td>TVPI 1.5-2x</td><td>1.0x initial amount</td></tr>
                        <tr><td>TVPI < 1.5x</td><td>0.2x initial amount</td></tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Simulation Parameters -->
        <div class="card">
            <h2>Simulation Parameters</h2>
            <div class="strategy-grid">
                <div class="input-group">
                    <label for="fundSize">Fund Size (millions)</label>
                    <input type="number" id="fundSize" value="400" min="100" max="2000" step="50">
                </div>
                <div class="input-group">
                    <label for="numTrials">Monte Carlo Trials</label>
                    <input type="number" id="numTrials" value="10000" min="1000" max="50000" step="1000">
                </div>
                <div class="input-group">
                    <label for="targetNetTVPI">Target Net TVPI</label>
                    <input type="number" id="targetNetTVPI" value="2.0" min="1.5" max="5" step="0.1">
                </div>
                <div class="input-group">
                    <button onclick="runStrategyComparison()">🧪 Test All Strategies</button>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="results" id="results">
            <!-- Quick Summary -->
            <div class="card">
                <h2>🏆 Strategy Comparison Results</h2>
                <div class="results-grid" id="strategyResults">
                    <!-- Results will be populated here -->
                </div>
            </div>

            <!-- Detailed Comparison -->
            <div class="card">
                <h2>📊 Detailed Performance Comparison</h2>
                <table class="comparison-table" id="comparisonTable">
                    <!-- Comparison table will be populated here -->
                </table>
            </div>

            <!-- Charts -->
            <div class="card">
                <h2>📈 Performance Analysis</h2>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>

            <!-- Insights -->
            <div class="card">
                <h2>💡 Strategy Insights</h2>
                <div id="strategyInsights">
                    <!-- Insights will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedStrategy = 1;
        let performanceChart = null;

        // Check sizes and stage parameters from the other simulators
        const CHECK_SIZES = {
            "Seed": { min: 1_000_000, max: 5_000_000, avg: 3_000_000 },
            "SeriesA": { min: 7_000_000, max: 15_000_000, avg: 11_000_000 },
            "SeriesB": { min: 10_000_000, max: 25_000_000, avg: 17_500_000 },
            "SeriesC": { min: 15_000_000, max: 40_000_000, avg: 27_500_000 },
            "Growth": { min: 30_000_000, max: 50_000_000, avg: 40_000_000 }
        };

        const STAGE_PARAMS = {
            "Seed":    [0.65, 0.70, 1.00, 2.3, 10.0, 0.15],
            "SeriesA": [0.35, 0.90, 0.80, 2.5, 10.0, 0.10],
            "SeriesB": [0.20, 1.10, 0.60, 2.8, 10.0, 0.06],
            "SeriesC": [0.10, 1.00, 0.60, 2.5, 8.0, 0.08],
            "Growth":  [0.05, 0.95, 0.45, 2.8, 7.0, 0.05],
        };

        // Follow-on sizing strategies
        const FOLLOW_ON_STRATEGIES = {
            1: { // Intuitive Strategy
                name: "Intuitive Strategy",
                getFollowOnSize: (initialAmount, initialStage, nextStage, tvpi) => {
                    const transitions = {
                        "Seed_SeriesA": 2.5,
                        "SeriesA_SeriesB": 1.0,
                        "SeriesB_SeriesC": 0.7,
                        "SeriesC_Growth": 0.5
                    };
                    const key = `${initialStage}_${nextStage}`;
                    return initialAmount * (transitions[key] || 1.0);
                }
            },
            2: { // Current Model
                name: "Current Model",
                getFollowOnSize: (initialAmount, initialStage, nextStage, tvpi) => {
                    const multipliers = {
                        "Seed": 2.0,
                        "SeriesA": 1.6,
                        "SeriesB": 1.4,
                        "SeriesC": 1.2,
                        "Growth": 0.8
                    };
                    return initialAmount * (multipliers[initialStage] || 1.0);
                }
            },
            3: { // Conservative
                name: "Conservative Strategy",
                getFollowOnSize: (initialAmount, initialStage, nextStage, tvpi) => {
                    return initialAmount; // Always 1x
                }
            },
            4: { // Aggressive Winner
                name: "Aggressive Winner Strategy",
                getFollowOnSize: (initialAmount, initialStage, nextStage, tvpi) => {
                    if (tvpi >= 3) return initialAmount * 3.0;
                    if (tvpi >= 2) return initialAmount * 2.0;
                    if (tvpi >= 1.5) return initialAmount * 1.0;
                    return initialAmount * 0.2;
                }
            }
        };

        function selectStrategy(strategyNum) {
            selectedStrategy = strategyNum;
            document.querySelectorAll('.strategy-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelectorAll('.strategy-card')[strategyNum - 1].classList.add('active');
        }

        // Statistical functions (copied from other simulators)
        function normalCDF(x, mean, stdDev) {
            const z = (x - mean) / stdDev;
            const t = 1 / (1 + 0.2316419 * Math.abs(z));
            const d = 0.3989423 * Math.exp(-z * z / 2);
            const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
            return z > 0 ? 1 - prob : prob;
        }

        function normalPPF(p, mean, stdDev) {
            if (p <= 0) return -Infinity;
            if (p >= 1) return Infinity;
            
            const a1 = -3.969683028665376e+01;
            const a2 = 2.209460984245205e+02;
            const a3 = -2.759285104469687e+02;
            const a4 = 1.383577518672690e+02;
            const a5 = -3.066479806614716e+01;
            const a6 = 2.506628277459239e+00;
            
            const b1 = -5.447609879822406e+01;
            const b2 = 1.615858368580409e+02;
            const b3 = -1.556989798598866e+02;
            const b4 = 6.680131188771972e+01;
            const b5 = -1.328068155288572e+01;
            
            const c1 = -7.784894002430293e-03;
            const c2 = -3.223964580411365e-01;
            const c3 = -2.400758277161838e+00;
            const c4 = -2.549732539343734e+00;
            const c5 = 4.374664141464968e+00;
            const c6 = 2.938163982698783e+00;
            
            const d1 = 7.784695709041462e-03;
            const d2 = 3.224671290700398e-01;
            const d3 = 2.445134137142996e+00;
            const d4 = 3.754408661907416e+00;
            
            let q, r;
            
            if (p < 0.02425) {
                q = Math.sqrt(-2 * Math.log(p));
                return mean + stdDev * (((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / 
                       ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
            } else if (p < 0.97575) {
                q = p - 0.5;
                r = q * q;
                return mean + stdDev * (((((a1 * r + a2) * r + a3) * r + a4) * r + a5) * r + a6) * q / 
                       (((((b1 * r + b2) * r + b3) * r + b4) * r + b5) * r + 1);
            } else {
                q = Math.sqrt(-2 * Math.log(1 - p));
                return mean + stdDev * -(((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / 
                       ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
            }
        }

        function drawMultiplesVectorized(stage, numDraws) {
            if (numDraws === 0) return [];
            
            const [p0, mu, sigma, alpha, xmin, tailWeight] = STAGE_PARAMS[stage];
            const results = new Array(numDraws).fill(0);
            
            for (let i = 0; i < numDraws; i++) {
                const randP0 = Math.random();
                
                if (randP0 < p0) {
                    results[i] = 0;
                } else {
                    const randTail = Math.random();
                    
                    if (randTail < tailWeight) {
                        const u = Math.random();
                        results[i] = xmin * Math.pow(1 - u, -1 / alpha);
                    } else {
                        const logXmin = Math.log(xmin);
                        const FLogXmin = normalCDF(logXmin, mu, sigma);
                        const u = Math.random();
                        const targetCDF = u * FLogXmin;
                        const logMultiple = normalPPF(targetCDF, mu, sigma);
                        results[i] = Math.min(Math.exp(logMultiple), xmin * (1 - 1e-10));
                    }
                }
            }
            
            return results;
        }

        function getRandomCheckSize(stage) {
            const range = CHECK_SIZES[stage];
            return range.min + Math.random() * (range.max - range.min);
        }

        // Updated follow-on probability based on 2022-2024 market data
        function getFollowOnProbability(tvpi, stage = "SeriesB") {
            if (tvpi === 0) return 0.05;  // Zombies rarely get follow-on
            
            // Base graduation rates by stage (from research data)
            const baseGraduationRates = {
                "Seed": 0.20,      // 15-20% Seed-to-A per recent data
                "SeriesA": 0.55,   // 50-60% A-to-B estimated
                "SeriesB": 0.60,   // ~60% B-to-C
                "SeriesC": 0.65,   // Higher for proven companies
                "Growth": 0.70     // Mature companies
            };
            
            const baseRate = baseGraduationRates[stage] || 0.50;
            
            // Performance multipliers
            let performanceMultiplier;
            
            if (tvpi < 0.5) {
                performanceMultiplier = 0.3;  // Severe underperformers
            } else if (tvpi < 1.0) {
                performanceMultiplier = 0.6;  // Below water
            } else if (tvpi < 1.5) {
                performanceMultiplier = 0.9;  // Modest performance
            } else if (tvpi < 2.0) {
                performanceMultiplier = 1.2;  // Good performance
            } else if (tvpi < 3.0) {
                performanceMultiplier = 1.5;  // Strong performance
            } else {
                // Exceptional performers - but stage matters
                if (stage === "Seed" || stage === "SeriesA") {
                    performanceMultiplier = 1.8;  // Early winners still attractive
                } else {
                    performanceMultiplier = 1.6;  // Later stage faces valuation concerns
                }
            }
            
            // Calculate probability
            let probability = baseRate * performanceMultiplier;
            
            // Stage-specific caps reflecting market reality
            if (stage === "Seed") {
                probability = Math.min(0.40, probability);  // Even great Seed faces Series A crunch
            } else if (stage === "Growth") {
                probability = Math.min(0.85, probability);  // Late stage has limits
            } else {
                probability = Math.min(0.95, probability);  // General cap
            }
            
            return probability;
        }

        // Stage progression mapping
        const STAGE_PROGRESSION = {
            "Seed": "SeriesA",
            "SeriesA": "SeriesB", 
            "SeriesB": "SeriesC",
            "SeriesC": "Growth",
            "Growth": "Growth"
        };

        function runStrategyComparison() {
            const fundSize = parseFloat(document.getElementById('fundSize').value) * 1_000_000;
            const numTrials = parseInt(document.getElementById('numTrials').value);
            const targetNetTVPI = parseFloat(document.getElementById('targetNetTVPI').value);

            document.getElementById('results').classList.add('show');
            
            // Show loading
            document.getElementById('strategyResults').innerHTML = '<p style="text-align: center; color: #7f8c8d;">Running simulations...</p>';

            setTimeout(() => {
                const results = {};
                
                // Test each strategy
                for (let strategyId = 1; strategyId <= 4; strategyId++) {
                    results[strategyId] = testStrategy(strategyId, fundSize, numTrials);
                }
                
                displayResults(results, targetNetTVPI);
                createChart(results);
                generateInsights(results, targetNetTVPI);
            }, 100);
        }

        function testStrategy(strategyId, fundSize, numTrials) {
            const strategy = FOLLOW_ON_STRATEGIES[strategyId];
            
            // CRITICAL FIX: Calculate portfolio size based on strategy's follow-on requirements
            const basePortfolio = {
                "Seed": 15,
                "SeriesA": 10,
                "SeriesB": 6,
                "SeriesC": 3,
                "Growth": 1
            };
            
            // Estimate follow-on capital requirements for each strategy
            let estimatedFollowOnRatio = 0;
            if (strategyId === 1) { // Intuitive
                estimatedFollowOnRatio = 1.3; // Weighted average of 2.5, 1.0, 0.7, 0.5
            } else if (strategyId === 2) { // Current Model
                estimatedFollowOnRatio = 1.5; // Weighted average of 2.0, 1.6, 1.4, 1.2, 0.8
            } else if (strategyId === 3) { // Conservative
                estimatedFollowOnRatio = 1.0; // Always 1x
            } else if (strategyId === 4) { // Aggressive Winner
                estimatedFollowOnRatio = 2.2; // High follow-on for winners
            }
            
            // Calculate base portfolio cost
            let basePortfolioCost = 0;
            for (const [stage, numDeals] of Object.entries(basePortfolio)) {
                basePortfolioCost += numDeals * CHECK_SIZES[stage].avg;
            }
            
            // FUND CONSTRAINT: Max deployment = 105% of fund
            const maxDeployment = fundSize * 1.05;
            
            // Expected total deployment = initial + (initial * followOnRatio * followOnProbability)
            const avgFollowOnProbability = 0.4; // Average across performance levels
            const expectedTotalDeployment = basePortfolioCost * (1 + estimatedFollowOnRatio * avgFollowOnProbability);
            
            // Scale down portfolio if it would exceed fund constraints
            const portfolioScalingFactor = Math.min(1.0, maxDeployment / expectedTotalDeployment);
            
            // Create scaled portfolio
            const portfolioMix = {};
            for (const [stage, numDeals] of Object.entries(basePortfolio)) {
                portfolioMix[stage] = Math.max(1, Math.round(numDeals * portfolioScalingFactor));
            }

            console.log(`\n=== Strategy ${strategyId}: ${strategy.name} ===`);
            console.log(`Portfolio scaling factor: ${portfolioScalingFactor.toFixed(3)}`);
            console.log(`Expected follow-on ratio: ${estimatedFollowOnRatio.toFixed(1)}:1`);
            console.log(`Scaled portfolio:`, portfolioMix);

            const trialResults = [];
            let totalInitialCapital = 0;
            let totalFollowOnCapital = 0;
            let totalValue = 0;

            for (let trial = 0; trial < numTrials; trial++) {
                let trialInitialCapital = 0;
                let trialFollowOnCapital = 0;
                let trialValue = 0;
                let remainingCapital = maxDeployment;
                
                const investments = [];
                
                // Phase 1: Make initial investments
                for (const [stage, numDeals] of Object.entries(portfolioMix)) {
                    for (let i = 0; i < numDeals; i++) {
                        const initialAmount = getRandomCheckSize(stage);
                        
                        if (initialAmount <= remainingCapital) {
                            trialInitialCapital += initialAmount;
                            remainingCapital -= initialAmount;
                            
                            // Get initial performance
                            const initialMultiple = drawMultiplesVectorized(stage, 1)[0];
                            trialValue += initialAmount * initialMultiple;
                            
                            investments.push({
                                stage,
                                initialAmount,
                                initialMultiple,
                                followOnDone: false
                            });
                        }
                    }
                }
                
                // Phase 2: Make follow-on investments (within remaining capital)
                for (const investment of investments) {
                    if (Math.random() < getFollowOnProbability(investment.initialMultiple, investment.stage)) {
                        const nextStage = STAGE_PROGRESSION[investment.stage];
                        const followOnAmount = strategy.getFollowOnSize(
                            investment.initialAmount, 
                            investment.stage, 
                            nextStage, 
                            investment.initialMultiple
                        );
                        
                        // Only invest if we have remaining capital
                        if (followOnAmount <= remainingCapital) {
                            trialFollowOnCapital += followOnAmount;
                            remainingCapital -= followOnAmount;
                            investment.followOnDone = true;
                            
                            // Get follow-on performance
                            const followOnMultiple = drawMultiplesVectorized(nextStage, 1)[0];
                            trialValue += followOnAmount * followOnMultiple;
                        }
                    }
                }
                
                totalInitialCapital += trialInitialCapital;
                totalFollowOnCapital += trialFollowOnCapital;
                totalValue += trialValue;
                
                const totalInvested = trialInitialCapital + trialFollowOnCapital;
                const tvpi = totalInvested > 0 ? trialValue / totalInvested : 0;
                trialResults.push({ 
                    initialCapital: trialInitialCapital,
                    followOnCapital: trialFollowOnCapital,
                    totalInvested: totalInvested,
                    totalValue: trialValue,
                    tvpi: tvpi,
                    deploymentRate: totalInvested / fundSize
                });
            }

            // Calculate statistics
            const avgInitialCapital = totalInitialCapital / numTrials;
            const avgFollowOnCapital = totalFollowOnCapital / numTrials;
            const avgTotalInvested = avgInitialCapital + avgFollowOnCapital;
            const avgTotalValue = totalValue / numTrials;
            const avgTVPI = avgTotalInvested > 0 ? avgTotalValue / avgTotalInvested : 0;
            
            // Calculate portfolio metrics
            const deploymentRate = avgTotalInvested / fundSize;
            const dryPowder = Math.max(0, fundSize - avgTotalInvested);
            const dryPowderPercent = (dryPowder / fundSize) * 100;
            
            // Calculate actual company count
            const actualCompanyCount = Object.values(portfolioMix).reduce((sum, count) => sum + count, 0);
            
            // Calculate net TVPI (simplified)
            const managementFees = fundSize * 0.02 * 10; // 2% for 10 years
            const grossReturn = avgTotalValue;
            const profits = Math.max(0, grossReturn - fundSize);
            const carriedInterest = profits * 0.20;
            const netReturn = grossReturn - managementFees - carriedInterest;
            const netTVPI = netReturn / fundSize;

            console.log(`Results: Gross TVPI ${avgTVPI.toFixed(2)}x, Net TVPI ${netTVPI.toFixed(2)}x, Deployment ${(deploymentRate*100).toFixed(1)}%`);
            console.log(`Companies: ${actualCompanyCount}, Dry Powder: ${dryPowderPercent.toFixed(1)}%`);

            return {
                strategyName: strategy.name,
                avgTVPI: avgTVPI,
                netTVPI: netTVPI,
                avgInitialCapital: avgInitialCapital,
                avgFollowOnCapital: avgFollowOnCapital,
                avgTotalInvested: avgTotalInvested,
                deploymentRate: deploymentRate,
                dryPowder: dryPowder,
                dryPowderPercent: dryPowderPercent,
                followOnRatio: avgFollowOnCapital / avgInitialCapital,
                actualCompanyCount: actualCompanyCount,
                portfolioScalingFactor: portfolioScalingFactor,
                trialResults: trialResults
            };
        }

        function displayResults(results, targetNetTVPI) {
            const container = document.getElementById('strategyResults');
            container.innerHTML = '';

            // Find best strategy
            let bestStrategy = null;
            let bestScore = -1;
            
            for (const [strategyId, result] of Object.entries(results)) {
                // Score based on net TVPI achievement and capital efficiency
                const score = result.netTVPI + (1 - result.dryPowderPercent / 100);
                if (score > bestScore) {
                    bestScore = score;
                    bestStrategy = strategyId;
                }
            }

            // Display strategy cards
            for (const [strategyId, result] of Object.entries(results)) {
                const isWinner = strategyId === bestStrategy;
                const meetsTarget = result.netTVPI >= targetNetTVPI;
                
                const card = document.createElement('div');
                card.className = `result-card ${isWinner ? 'winner' : ''}`;
                card.innerHTML = `
                    <h3>${result.strategyName} ${isWinner ? '🏆' : ''}</h3>
                    <div class="metric-value ${isWinner ? 'winner' : ''}">${result.avgTVPI.toFixed(2)}x</div>
                    <div class="metric-label">Gross TVPI</div>
                    <div style="margin-top: 15px;">
                        <strong>Net TVPI:</strong> ${result.netTVPI.toFixed(2)}x ${meetsTarget ? '✅' : '❌'}<br>
                        <strong>Follow-On Ratio:</strong> ${result.followOnRatio.toFixed(1)}:1<br>
                        <strong>Deployment:</strong> ${(result.deploymentRate * 100).toFixed(1)}%<br>
                        <strong>Dry Powder:</strong> ${result.dryPowderPercent.toFixed(1)}%
                    </div>
                `;
                container.appendChild(card);
            }

            // Create comparison table
            createComparisonTable(results, bestStrategy);
        }

        function createComparisonTable(results, bestStrategy) {
            const table = document.getElementById('comparisonTable');
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>Strategy</th>
                        <th>Gross TVPI</th>
                        <th>Net TVPI</th>
                        <th>Follow-On Ratio</th>
                        <th>Deployment %</th>
                        <th>Dry Powder %</th>
                        <th>Capital Efficiency</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            `;

            const tbody = table.querySelector('tbody');
            
            for (const [strategyId, result] of Object.entries(results)) {
                const isWinner = strategyId === bestStrategy;
                const capitalEfficiency = result.avgTVPI * result.deploymentRate;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="${isWinner ? 'best' : ''}">${result.strategyName}</td>
                    <td class="${isWinner ? 'best' : ''}">${result.avgTVPI.toFixed(2)}x</td>
                    <td class="${isWinner ? 'best' : ''}">${result.netTVPI.toFixed(2)}x</td>
                    <td>${result.followOnRatio.toFixed(1)}:1</td>
                    <td>${(result.deploymentRate * 100).toFixed(1)}%</td>
                    <td>${result.dryPowderPercent.toFixed(1)}%</td>
                    <td class="${isWinner ? 'best' : ''}">${capitalEfficiency.toFixed(2)}</td>
                `;
                tbody.appendChild(row);
            }
        }

        function createChart(results) {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            if (performanceChart) {
                performanceChart.destroy();
            }
            
            const strategyNames = Object.values(results).map(r => r.strategyName);
            const grossTVPIs = Object.values(results).map(r => r.avgTVPI);
            const netTVPIs = Object.values(results).map(r => r.netTVPI);
            const dryPowderPercentages = Object.values(results).map(r => r.dryPowderPercent);

            performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: strategyNames,
                    datasets: [
                        {
                            label: 'Gross TVPI',
                            data: grossTVPIs,
                            backgroundColor: 'rgba(52, 152, 219, 0.6)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 2,
                            yAxisID: 'y-tvpi'
                        },
                        {
                            label: 'Net TVPI',
                            data: netTVPIs,
                            backgroundColor: 'rgba(46, 204, 113, 0.6)',
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 2,
                            yAxisID: 'y-tvpi'
                        },
                        {
                            label: 'Dry Powder %',
                            data: dryPowderPercentages,
                            backgroundColor: 'rgba(231, 76, 60, 0.6)',
                            borderColor: 'rgba(231, 76, 60, 1)',
                            borderWidth: 2,
                            yAxisID: 'y-percent',
                            type: 'line'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Follow-On Strategy Performance Comparison'
                        }
                    },
                    scales: {
                        'y-tvpi': {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'TVPI Multiple'
                            }
                        },
                        'y-percent': {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Dry Powder %'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });
        }

        function generateInsights(results, targetNetTVPI) {
            const container = document.getElementById('strategyInsights');
            
            // Find best performing strategy
            let bestTVPI = 0;
            let bestStrategy = '';
            let bestDeployment = 0;
            let lowestDryPowder = 100;
            
            for (const result of Object.values(results)) {
                if (result.avgTVPI > bestTVPI) {
                    bestTVPI = result.avgTVPI;
                    bestStrategy = result.strategyName;
                }
                if (result.deploymentRate > bestDeployment) {
                    bestDeployment = result.deploymentRate;
                }
                if (result.dryPowderPercent < lowestDryPowder) {
                    lowestDryPowder = result.dryPowderPercent;
                }
            }

            let insights = '<h3>🔍 Key Insights</h3>';
            
            insights += `<p><strong>Best Performing Strategy:</strong> ${bestStrategy} achieved the highest gross TVPI of ${bestTVPI.toFixed(2)}x.</p>`;
            
            const targetMeetingStrategies = Object.values(results).filter(r => r.netTVPI >= targetNetTVPI);
            if (targetMeetingStrategies.length > 0) {
                insights += `<p><strong>Target Achievement:</strong> ${targetMeetingStrategies.length} strategies met the ${targetNetTVPI}x net TVPI target.</p>`;
            } else {
                insights += `<p><strong>Target Gap:</strong> No strategy achieved the ${targetNetTVPI}x net TVPI target. Consider adjusting expectations or portfolio composition.</p>`;
            }
            
            insights += `<p><strong>Capital Efficiency:</strong> The most efficient deployment rate was ${(bestDeployment * 100).toFixed(1)}% with lowest dry powder at ${lowestDryPowder.toFixed(1)}%.</p>`;
            
            // Strategy-specific insights
            const intuitiveResult = results[1];
            const currentResult = results[2];
            
            if (intuitiveResult.avgTVPI > currentResult.avgTVPI) {
                insights += `<p><strong>Strategy Validation:</strong> Your intuitive approach outperformed the current model by ${((intuitiveResult.avgTVPI / currentResult.avgTVPI - 1) * 100).toFixed(1)}% in gross TVPI.</p>`;
            }

            container.innerHTML = insights;
        }
    </script>
</body>
</html> 