<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Forecaster & Optimizer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            color: #1f1f1f;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            color: #1f1f1f;
            margin-bottom: 8px;
            font-size: 2.5em;
            font-weight: 400;
            letter-spacing: -0.5px;
        }

        .subtitle {
            text-align: center;
            color: #5f6368;
            margin-bottom: 32px;
            font-size: 1.1em;
            font-weight: 400;
        }

        .card {
            background: #ffffff;
            border-radius: 24px;
            padding: 24px;
            margin-bottom: 16px;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 1px 3px 1px rgba(60,64,67,0.15);
            border: 1px solid #e8eaed;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .section-number {
            background: #1a73e8;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            margin-right: 16px;
        }

        .section-title {
            font-size: 1.5em;
            font-weight: 500;
            color: #1f1f1f;
        }

        .input-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1f1f1f;
            font-size: 0.875em;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: #ffffff;
            font-family: 'Roboto', sans-serif;
        }

        .input-group input:hover, .input-group select:hover {
            border-color: #1f1f1f;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #1a73e8;
            border-width: 2px;
            padding: 11px 15px;
        }

        .button {
            padding: 12px 24px;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 100px;
            font-size: 0.875em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            letter-spacing: 0.25px;
            font-family: 'Google Sans', 'Roboto', sans-serif;
        }

        .button:hover {
            background: #1765cc;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 2px 6px 2px rgba(60,64,67,0.15);
        }

        .button-secondary {
            background: #ffffff;
            color: #1a73e8;
            border: 1px solid #dadce0;
        }

        .button-secondary:hover {
            background: #f8f9fa;
            border-color: #1a73e8;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .metric-card {
            background: #ffffff;
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            border: 1px solid #e8eaed;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 1px 3px 1px rgba(60,64,67,0.15);
        }

        .metric-value {
            font-size: 2em;
            font-weight: 500;
            color: #1f1f1f;
            margin-bottom: 8px;
        }

        .metric-label {
            color: #5f6368;
            font-size: 0.875em;
            font-weight: 400;
        }

        .chart-container {
            height: 400px;
            margin: 24px 0;
            background: white;
            border-radius: 16px;
            padding: 16px;
            border: 1px solid #e8eaed;
        }

        .investments-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid #e8eaed;
        }

        .investments-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .investments-table th,
        .investments-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8eaed;
        }

        .investments-table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #1f1f1f;
            font-size: 0.875em;
        }

        .investments-table td {
            font-size: 0.875em;
            color: #5f6368;
        }

        .scenario-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin: 24px 0;
        }

        .scenario-card {
            background: #ffffff;
            border: 2px solid #e8eaed;
            border-radius: 16px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .scenario-card:hover {
            border-color: #1a73e8;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 2px 6px 2px rgba(60,64,67,0.15);
        }

        .scenario-card.selected {
            border-color: #1a73e8;
            background: #e8f0fe;
        }

        .scenario-title {
            font-weight: 500;
            color: #1f1f1f;
            margin-bottom: 8px;
        }

        .scenario-description {
            color: #5f6368;
            font-size: 0.875em;
            line-height: 1.4;
        }

        .winner-highlights {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
        }

        .winner-highlights h3 {
            color: #2e7d32;
            margin-bottom: 16px;
            font-weight: 500;
        }

        .winners-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .winner-card {
            background: rgba(255, 255, 255, 0.8);
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #c8e6c9;
        }

        .hidden {
            display: none;
        }

        .scenario-breakdown {
            overflow-x: auto;
        }

        .breakdown-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
            font-size: 0.875em;
        }

        .breakdown-table th,
        .breakdown-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8eaed;
        }

        .breakdown-table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #1f1f1f;
            position: sticky;
            top: 0;
        }

        .breakdown-table td {
            color: #5f6368;
        }

        .breakdown-table .scenario-name {
            font-weight: 500;
            color: #1f1f1f;
        }

        .breakdown-table .scenario-stop { border-left: 4px solid #ea4335; }
        .breakdown-table .scenario-conservative { border-left: 4px solid #fbbc04; }
        .breakdown-table .scenario-aggressive { border-left: 4px solid #34a853; }
        .breakdown-table .scenario-followon { border-left: 4px solid #1a73e8; }

        .step-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e8eaed;
        }

        .progress-bar {
            height: 4px;
            background: #e8eaed;
            border-radius: 2px;
            margin-bottom: 32px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #1a73e8;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .input-grid {
                grid-template-columns: 1fr;
            }
            
            .performance-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .scenario-cards {
                grid-template-columns: 1fr;
            }
            
            /* Stack charts vertically on mobile */
            div[style*="grid-template-columns: 1fr 1fr"] {
                display: block !important;
            }
            
            div[style*="grid-template-columns: 1fr 1fr"] .chart-container {
                margin-bottom: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Portfolio Forecaster & Optimizer</h1>
        <p class="subtitle">Real-time fund performance analysis and strategic deployment optimization</p>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 20%;"></div>
        </div>

        <!-- Step 1: Fund Setup -->
        <div class="step" id="step1">
            <div class="card">
                <div class="section-header">
                    <div class="section-number">1</div>
                    <h2 class="section-title">Fund Setup</h2>
                </div>
                
                <div class="input-grid">
                    <div class="input-group">
                        <label for="fundName">Fund Name</label>
                        <input type="text" id="fundName" placeholder="Fund II, L.P.">
                    </div>
                    
                    <div class="input-group">
                        <label for="fundSize">Fund Size ($M)</label>
                        <input type="number" id="fundSize" value="400" min="1" max="10000">
                    </div>
                    
                    <div class="input-group">
                        <label for="vintage">Vintage Year</label>
                        <input type="number" id="vintage" value="2022" min="2015" max="2025">
                    </div>
                    
                    <div class="input-group">
                        <label for="managementFee">Management Fee (%)</label>
                        <input type="number" id="managementFee" value="2.0" min="0" max="5" step="0.1">
                    </div>
                    
                    <div class="input-group">
                        <label for="carry">Carried Interest (%)</label>
                        <input type="number" id="carry" value="20" min="0" max="30" step="1">
                    </div>
                    
                    <div class="input-group">
                        <label for="fundLife">Fund Life (Years)</label>
                        <input type="number" id="fundLife" value="10" min="5" max="15">
                    </div>
                </div>
                
                <div class="step-nav">
                    <div></div>
                    <button class="button" onclick="nextStep()">Next: Add Investments →</button>
                </div>
            </div>
        </div>

        <!-- Step 2: Investment Data Entry -->
        <div class="step hidden" id="step2">
            <div class="card">
                <div class="section-header">
                    <div class="section-number">2</div>
                    <h2 class="section-title">Portfolio Investments</h2>
                </div>
                
                <div class="input-grid">
                    <div class="input-group">
                        <label for="companyName">Company Name</label>
                        <input type="text" id="companyName" placeholder="Company Inc.">
                    </div>
                    
                    <div class="input-group">
                        <label for="investmentDate">Investment Date</label>
                        <input type="date" id="investmentDate">
                    </div>
                    
                    <div class="input-group">
                        <label for="stage">Series</label>
                        <select id="stage">
                            <option value="Seed">Seed</option>
                            <option value="SeriesA">Series A</option>
                            <option value="SeriesB">Series B</option>
                            <option value="SeriesC">Series C</option>
                            <option value="Growth">Growth</option>
                            <option value="SAFE">SAFE</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label for="checkSize">Check Size ($M)</label>
                        <input type="number" id="checkSize" min="0.1" step="0.1">
                    </div>
                    
                    <div class="input-group">
                        <label for="preMoney">Pre-Money Valuation ($M)</label>
                        <input type="number" id="preMoney" min="1" step="1">
                    </div>
                    
                    <div class="input-group">
                        <label for="currentValue">Current Value ($M)</label>
                        <input type="number" id="currentValue" min="0" step="0.1">
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <button class="button" onclick="addInvestment()">+ Add Investment</button>
                    <button class="button-secondary button" onclick="loadSampleData()" style="margin-left: 12px;">Load Sample Data</button>
                </div>

                <div class="investments-table" id="investmentsTable">
                    <table>
                        <thead>
                            <tr>
                                <th>Company</th>
                                <th>Date</th>
                                <th>Series</th>
                                <th>Check Size</th>
                                <th>Pre-Money</th>
                                <th>Current Value</th>
                                <th>Multiple</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="investmentsTableBody">
                        </tbody>
                    </table>
                </div>
                
                <div class="step-nav">
                    <button class="button-secondary button" onclick="prevStep()">← Back</button>
                    <button class="button" onclick="nextStep()">Next: Performance Dashboard →</button>
                </div>
            </div>
        </div>

        <!-- Step 3: Performance Dashboard -->
        <div class="step hidden" id="step3">
            <div class="card">
                <div class="section-header">
                    <div class="section-number">3</div>
                    <h2 class="section-title">Current Performance</h2>
                </div>
                
                <div class="performance-grid" id="performanceGrid">
                    <!-- Performance metrics will be populated here -->
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin: 24px 0;">
                    <div class="chart-container">
                        <canvas id="jCurveChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="irrChart"></canvas>
                    </div>
                </div>
                
                <div class="winner-highlights" id="winnerHighlights">
                    <!-- Winner analysis will be populated here -->
                </div>
                
                <div class="step-nav">
                    <button class="button-secondary button" onclick="prevStep()">← Back</button>
                    <button class="button" onclick="nextStep()">Next: Forecasting →</button>
                </div>
            </div>
        </div>

        <!-- Step 4: Forecasting Scenarios -->
        <div class="step hidden" id="step4">
            <div class="card">
                <div class="section-header">
                    <div class="section-number">4</div>
                    <h2 class="section-title">Portfolio Forecasting</h2>
                </div>
                
                <div class="scenario-cards">
                    <div class="scenario-card selected" data-scenario="stop">
                        <div class="scenario-title">🛑 Stop Investing</div>
                        <div class="scenario-description">Deploy no additional capital. Only follow-on existing winners.</div>
                    </div>
                    
                    <div class="scenario-card" data-scenario="conservative">
                        <div class="scenario-title">🎯 Conservative Deployment</div>
                        <div class="scenario-description">Deploy 50% remaining capital, hold 50% for follow-ons.</div>
                    </div>
                    
                    <div class="scenario-card" data-scenario="aggressive">
                        <div class="scenario-title">🚀 Aggressive Deployment</div>
                        <div class="scenario-description">Deploy 80% remaining capital into new deals.</div>
                    </div>
                    
                    <div class="scenario-card" data-scenario="followon">
                        <div class="scenario-title">💰 Follow-On Focused</div>
                        <div class="scenario-description">Hold 70% for follow-ons, minimal new investments.</div>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin: 24px 0;">
                    <div class="chart-container">
                        <canvas id="forecastChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="forecastIRRChart"></canvas>
                    </div>
                </div>
                
                <div class="card" style="margin-top: 24px;">
                    <h3 style="margin-bottom: 16px; color: #1f1f1f;">📋 Scenario Assumptions & Projections</h3>
                    <div class="scenario-breakdown" id="scenarioBreakdown">
                        <!-- Scenario breakdown table will be populated here -->
                    </div>
                </div>
                
                <div class="card" style="margin-top: 24px;">
                    <h3 style="margin-bottom: 16px; color: #1f1f1f;">🔍 Investment-by-Investment Analysis</h3>
                    <p style="margin-bottom: 16px; color: #5f6368; font-size: 0.875em;">
                        Detailed breakdown showing projected performance for each investment in the selected scenario
                    </p>
                    <div class="investment-details" id="investmentDetails">
                        <!-- Investment details will be populated here -->
                    </div>
                </div>
                
                <div class="step-nav">
                    <button class="button-secondary button" onclick="prevStep()">← Back</button>
                    <button class="button" onclick="nextStep()">Next: Recommendations →</button>
                </div>
            </div>
        </div>

        <!-- Step 5: Optimization Recommendations -->
        <div class="step hidden" id="step5">
            <div class="card">
                <div class="section-header">
                    <div class="section-number">5</div>
                    <h2 class="section-title">Strategic Recommendations</h2>
                </div>
                
                <div id="recommendations">
                    <!-- Recommendations will be populated here -->
                </div>
                
                <div class="step-nav">
                    <button class="button-secondary button" onclick="prevStep()">← Back</button>
                    <button class="button" onclick="exportReport()">📊 Export Report</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let fundData = {};
        let investments = [];
        let selectedScenario = 'stop';
        
        // Stage parameters for modeling
        const STAGE_PARAMS = {
            "Seed": {
                checkSizes: { min: 1, max: 5, avg: 3 },
                returns: [0.65, 0.70, 1.00, 2.3, 10.0, 0.15],
                graduationBase: 0.15,
                nextStage: "SeriesA"
            },
            "SeriesA": {
                checkSizes: { min: 7, max: 15, avg: 11 },
                returns: [0.35, 0.90, 0.80, 2.5, 10.0, 0.10],
                graduationBase: 0.45,
                nextStage: "SeriesB"
            },
            "SeriesB": {
                checkSizes: { min: 10, max: 25, avg: 17.5 },
                returns: [0.20, 1.10, 0.60, 2.8, 10.0, 0.06],
                graduationBase: 0.50,
                nextStage: "SeriesC"
            },
            "SeriesC": {
                checkSizes: { min: 15, max: 40, avg: 27.5 },
                returns: [0.10, 1.00, 0.60, 2.5, 8.0, 0.08],
                graduationBase: 0.55,
                nextStage: "Growth"
            },
            "Growth": {
                checkSizes: { min: 30, max: 50, avg: 40 },
                returns: [0.05, 0.95, 0.45, 2.8, 7.0, 0.05],
                graduationBase: 0.60,
                nextStage: "Growth"
            },
            "SAFE": {
                checkSizes: { min: 1, max: 10, avg: 5 },
                returns: [0.65, 0.70, 1.00, 2.3, 10.0, 0.15],
                graduationBase: 0.15,
                nextStage: "SeriesA"
            }
        };

        function nextStep() {
            if (currentStep < 5) {
                // Validate current step before moving forward
                if (validateStep(currentStep)) {
                    document.getElementById(`step${currentStep}`).classList.add('hidden');
                    currentStep++;
                    document.getElementById(`step${currentStep}`).classList.remove('hidden');
                    updateProgress();
                    
                    // Trigger step-specific actions
                    if (currentStep === 3) {
                        calculatePerformance();
                    } else if (currentStep === 4) {
                        generateForecasts();
                    } else if (currentStep === 5) {
                        generateRecommendations();
                    }
                }
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                document.getElementById(`step${currentStep}`).classList.add('hidden');
                currentStep--;
                document.getElementById(`step${currentStep}`).classList.remove('hidden');
                updateProgress();
            }
        }

        function updateProgress() {
            const progress = (currentStep / 5) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        function validateStep(step) {
            if (step === 1) {
                // Validate fund setup
                fundData = {
                    name: document.getElementById('fundName').value || 'Fund II',
                    size: parseFloat(document.getElementById('fundSize').value) * 1e6,
                    vintage: parseInt(document.getElementById('vintage').value),
                    managementFee: parseFloat(document.getElementById('managementFee').value) / 100,
                    carry: parseFloat(document.getElementById('carry').value) / 100,
                    fundLife: parseInt(document.getElementById('fundLife').value)
                };
                return fundData.size > 0;
            } else if (step === 2) {
                // Validate investments
                return investments.length > 0;
            }
            return true;
        }

        function addInvestment() {
            const company = document.getElementById('companyName').value;
            const date = document.getElementById('investmentDate').value;
            const stage = document.getElementById('stage').value;
            const checkSize = parseFloat(document.getElementById('checkSize').value);
            const preMoney = parseFloat(document.getElementById('preMoney').value);
            const currentValue = parseFloat(document.getElementById('currentValue').value);

            if (!company || !date || !checkSize || !currentValue) {
                alert('Please fill in all required fields');
                return;
            }

            const investment = {
                id: Date.now(),
                company,
                date,
                stage,
                checkSize: checkSize * 1e6,
                preMoney: preMoney * 1e6,
                currentValue: currentValue * 1e6,
                multiple: currentValue / checkSize
            };

            investments.push(investment);
            updateInvestmentsTable();
            clearInvestmentForm();
        }

        function updateInvestmentsTable() {
            const tbody = document.getElementById('investmentsTableBody');
            tbody.innerHTML = '';

            investments.forEach(inv => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${inv.company}</td>
                    <td>${new Date(inv.date).toLocaleDateString()}</td>
                    <td>${inv.stage}</td>
                    <td>$${(inv.checkSize / 1e6).toFixed(1)}M</td>
                    <td>$${(inv.preMoney / 1e6).toFixed(1)}M</td>
                    <td>$${(inv.currentValue / 1e6).toFixed(1)}M</td>
                    <td style="color: ${inv.multiple >= 1 ? '#188038' : '#c5221f'};">${inv.multiple.toFixed(1)}x</td>
                    <td><button class="button-secondary button" onclick="removeInvestment(${inv.id})" style="padding: 6px 12px; font-size: 0.75em;">Remove</button></td>
                `;
                tbody.appendChild(row);
            });
        }

        function removeInvestment(id) {
            investments = investments.filter(inv => inv.id !== id);
            updateInvestmentsTable();
        }

        function clearInvestmentForm() {
            document.getElementById('companyName').value = '';
            document.getElementById('investmentDate').value = '';
            document.getElementById('checkSize').value = '';
            document.getElementById('preMoney').value = '';
            document.getElementById('currentValue').value = '';
        }

        function loadSampleData() {
            // Load the user's actual portfolio data
            const sampleInvestments = [
                { company: 'Prime Trust', date: '2021-10-01', stage: 'SeriesA', checkSize: 30, preMoney: 100, currentValue: 0 },
                { company: 'Klover', date: '2022-01-15', stage: 'SeriesA', checkSize: 15, preMoney: 50, currentValue: 15 },
                { company: 'Atomic', date: '2022-03-01', stage: 'SeriesB', checkSize: 20, preMoney: 80, currentValue: 20 },
                { company: 'Lambda', date: '2022-06-01', stage: 'SeriesB', checkSize: 20, preMoney: 100, currentValue: 140 },
                { company: 'Torus', date: '2024-01-01', stage: 'SeriesA', checkSize: 3, preMoney: 15, currentValue: 25 },
                { company: 'Attain', date: '2022-04-01', stage: 'SeriesA', checkSize: 15, preMoney: 60, currentValue: 96 },
                { company: 'Paytient', date: '2022-02-01', stage: 'SeriesB', checkSize: 12, preMoney: 40, currentValue: 45 },
                { company: 'Cylinder', date: '2023-01-01', stage: 'SeriesB', checkSize: 15, preMoney: 50, currentValue: 19.5 }
            ];

            investments = [];
            sampleInvestments.forEach((inv, index) => {
                investments.push({
                    id: Date.now() + index,
                    company: inv.company,
                    date: inv.date,
                    stage: inv.stage,
                    checkSize: inv.checkSize * 1e6,
                    preMoney: inv.preMoney * 1e6,
                    currentValue: inv.currentValue * 1e6,
                    multiple: inv.currentValue / inv.checkSize
                });
            });

            updateInvestmentsTable();
        }

                 function calculatePerformance() {
             const totalInvested = investments.reduce((sum, inv) => sum + inv.checkSize, 0);
             const totalValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
             
             // Calculate management fees called (quarterly over fund life)
             const currentDate = new Date();
             const fundStartDate = new Date(fundData.vintage, 0, 1);
             const quartersElapsed = Math.floor((currentDate - fundStartDate) / (1000 * 60 * 60 * 24 * 91.25)); // ~91.25 days per quarter
             const managementFeesPerQuarter = (fundData.size * fundData.managementFee) / 4;
             const totalManagementFeesCalled = Math.min(quartersElapsed * managementFeesPerQuarter, fundData.size * fundData.managementFee * fundData.fundLife);
             
             // Called capital = invested capital + management fees called
             const calledCapital = totalInvested + totalManagementFeesCalled;
             
             // TVPI calculations (on called capital)
             const grossTVPI = totalValue / calledCapital;
             
             // Net calculations (after carry on profits)
             const totalProfits = Math.max(0, totalValue - calledCapital);
             const carryOnProfits = totalProfits * fundData.carry;
             const netValue = totalValue - carryOnProfits;
             const netTVPI = netValue / calledCapital;
             
             const dpi = 0; // No distributions yet
             const rvpi = netTVPI - dpi;
             
             // Calculate deployment rate (of total fund size)
             const deploymentRate = (calledCapital / fundData.size) * 100;
             
             // Calculate IRR (approximate using simple IRR formula)
             const irr = calculateIRR();
             
             // Find winners
             const winners = investments.filter(inv => inv.multiple >= 3.0);
             
             // Update performance grid
             updatePerformanceGrid({
                 grossTVPI,
                 netTVPI,
                 dpi,
                 rvpi,
                 irr,
                 deploymentRate,
                 totalInvested,
                 totalValue,
                 calledCapital,
                 totalManagementFeesCalled,
                 winners
             });
             
             // Generate J-curve
             generateJCurve();
         }

         function calculateIRR() {
             // Simple IRR approximation using investment dates and current values
             if (investments.length === 0) return 0;
             
             const currentDate = new Date();
             let totalWeightedReturn = 0;
             let totalWeight = 0;
             
             investments.forEach(inv => {
                 const investmentDate = new Date(inv.date);
                 const yearsHeld = (currentDate - investmentDate) / (365.25 * 24 * 60 * 60 * 1000);
                 
                 if (yearsHeld > 0 && inv.multiple > 0) {
                     const annualizedReturn = Math.pow(inv.multiple, 1/yearsHeld) - 1;
                     const weight = inv.checkSize;
                     
                     totalWeightedReturn += annualizedReturn * weight;
                     totalWeight += weight;
                 }
             });
             
             return totalWeight > 0 ? totalWeightedReturn / totalWeight : 0;
         }

                 function updatePerformanceGrid(metrics) {
             const grid = document.getElementById('performanceGrid');
             grid.innerHTML = `
                 <div class="metric-card">
                     <div class="metric-value">${metrics.grossTVPI.toFixed(2)}x</div>
                     <div class="metric-label">Gross TVPI</div>
                 </div>
                 <div class="metric-card">
                     <div class="metric-value">${metrics.netTVPI.toFixed(2)}x</div>
                     <div class="metric-label">Net TVPI</div>
                 </div>
                 <div class="metric-card">
                     <div class="metric-value">${(metrics.irr * 100).toFixed(1)}%</div>
                     <div class="metric-label">Net IRR</div>
                 </div>
                 <div class="metric-card">
                     <div class="metric-value">${metrics.dpi.toFixed(2)}x</div>
                     <div class="metric-label">DPI</div>
                 </div>
                 <div class="metric-card">
                     <div class="metric-value">${metrics.rvpi.toFixed(2)}x</div>
                     <div class="metric-label">RVPI</div>
                 </div>
                 <div class="metric-card">
                     <div class="metric-value">$${(metrics.calledCapital / 1e6).toFixed(0)}M</div>
                     <div class="metric-label">Called Capital</div>
                 </div>
                 <div class="metric-card">
                     <div class="metric-value">$${(metrics.totalInvested / 1e6).toFixed(0)}M</div>
                     <div class="metric-label">Invested</div>
                 </div>
                 <div class="metric-card">
                     <div class="metric-value">$${(metrics.totalManagementFeesCalled / 1e6).toFixed(1)}M</div>
                     <div class="metric-label">Mgmt Fees Called</div>
                 </div>
             `;
             
             // Update winner highlights
             updateWinnerHighlights(metrics.winners);
         }

        function updateWinnerHighlights(winners) {
            const container = document.getElementById('winnerHighlights');
            if (winners.length === 0) {
                container.innerHTML = `
                    <h3>🎯 Portfolio Analysis</h3>
                    <p>No 3x+ winners identified yet. Focus on supporting strongest performers.</p>
                `;
                return;
            }
            
            const winnersHtml = winners.map(winner => `
                <div class="winner-card">
                    <strong>${winner.company}</strong><br>
                    <span style="color: #2e7d32; font-weight: 500;">${winner.multiple.toFixed(1)}x</span><br>
                    <span style="font-size: 0.75em; color: #5f6368;">$${(winner.checkSize / 1e6).toFixed(1)}M → $${(winner.currentValue / 1e6).toFixed(1)}M</span>
                </div>
            `).join('');
            
            container.innerHTML = `
                <h3>🏆 Portfolio Winners (3x+ Returns)</h3>
                <div class="winners-grid">
                    ${winnersHtml}
                </div>
                <p style="margin-top: 16px; color: #2e7d32; font-weight: 500;">
                    ${winners.length} winner${winners.length > 1 ? 's' : ''} identified • High follow-on probability • Consider aggressive follow-on deployment
                </p>
            `;
        }

        function generateJCurve() {
            const ctx = document.getElementById('jCurveChart').getContext('2d');
            const irrCtx = document.getElementById('irrChart').getContext('2d');
            
            // Use ACTUAL current portfolio performance 
            const totalInvested = investments.reduce((sum, inv) => sum + inv.checkSize, 0);
            const totalValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
            
            // Calculate called capital and current TVPI using same logic as dashboard
            const currentDate = new Date();
            const fundStartDate = new Date(fundData.vintage, 0, 1);
            const quartersElapsed = Math.floor((currentDate - fundStartDate) / (1000 * 60 * 60 * 24 * 91.25));
            const managementFeesPerQuarter = (fundData.size * fundData.managementFee) / 4;
            const totalManagementFeesCalled = Math.min(quartersElapsed * managementFeesPerQuarter, fundData.size * fundData.managementFee * fundData.fundLife);
            const calledCapital = totalInvested + totalManagementFeesCalled;
            
            // Calculate actual current metrics
            const currentGrossTVPI = totalValue / calledCapital;
            const currentTotalProfits = Math.max(0, totalValue - calledCapital);
            const currentCarryOnProfits = currentTotalProfits * fundData.carry;
            const currentNetValue = totalValue - currentCarryOnProfits;
            const currentNetTVPI = currentNetValue / calledCapital;
            
            // Generate realistic historical progression to current state
            const numQuarters = Math.max(10, quartersElapsed + 1);
            const tvpiData = [];
            const labels = [];
            
            for (let q = 0; q < numQuarters; q++) {
                const year = fundData.vintage + Math.floor(q / 4);
                const quarter = (q % 4) + 1;
                labels.push(`Q${quarter} ${year}`);
                
                if (q === numQuarters - 1) {
                    // Last quarter = actual current Net TVPI
                    tvpiData.push(currentNetTVPI);
                } else {
                    // Generate realistic J-curve progression to current state
                    const progress = q / (numQuarters - 1);
                    let tvpi;
                    
                    if (currentNetTVPI < 1.0) {
                        // Underperforming fund - start low, maybe dip lower, then recover partially
                        if (progress < 0.3) {
                            tvpi = 0.9 - (progress * 0.4); // Dip to 0.5-0.7
                        } else {
                            tvpi = 0.5 + (progress - 0.3) * (currentNetTVPI - 0.5) / 0.7;
                        }
                    } else {
                        // Normal J-curve progression
                        tvpi = 0.8 + (progress * progress) * (currentNetTVPI - 0.8);
                    }
                    
                    tvpiData.push(Math.max(0.1, tvpi));
                }
            }
            
            // Calculate IRR for each quarter based on actual progression
            const irrData = tvpiData.map((tvpi, index) => {
                const quarters = index + 1;
                const years = Math.max(0.25, quarters / 4); // Minimum 0.25 years
                
                // Use same IRR calculation as performance dashboard
                if (tvpi <= 0) return -50;
                
                const irr = Math.pow(tvpi, 1/years) - 1;
                return Math.max(-50, Math.min(100, irr * 100));
            });
            
            // TVPI Chart
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Fund Net TVPI',
                        data: tvpiData,
                        borderColor: '#1a73e8',
                        backgroundColor: 'rgba(26, 115, 232, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `Fund TVPI Over Time (Current: ${currentNetTVPI.toFixed(2)}x)`
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Net TVPI Multiple'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2) + 'x';
                                }
                            }
                        }
                    }
                }
            });
            
            // IRR Chart
            new Chart(irrCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Fund Net IRR',
                        data: irrData,
                        borderColor: '#34a853',
                        backgroundColor: 'rgba(52, 168, 83, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `Fund IRR Over Time (Current: ${irrData[irrData.length-1].toFixed(1)}%)`
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Net IRR (%)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(1) + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        function generateForecasts() {
            // Setup scenario selection
            document.querySelectorAll('.scenario-card').forEach(card => {
                card.addEventListener('click', function() {
                    document.querySelectorAll('.scenario-card').forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedScenario = this.dataset.scenario;
                    updateForecastChart();
                });
            });
            
            updateForecastChart();
        }

        function updateForecastChart() {
            const ctx = document.getElementById('forecastChart').getContext('2d');
            const irrCtx = document.getElementById('forecastIRRChart').getContext('2d');
            
            // Generate scenario-specific forecasts
            const scenarios = generateScenarioData();
            
            // TVPI Chart
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Current', '1 Year', '2 Years', '3 Years', '4 Years', '5 Years'],
                    datasets: scenarios.tvpiScenarios
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'TVPI Forecast by Scenario'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Net TVPI Multiple'
                            }
                        }
                    }
                }
            });
            
            // IRR Chart
            new Chart(irrCtx, {
                type: 'line',
                data: {
                    labels: ['Current', '1 Year', '2 Years', '3 Years', '4 Years', '5 Years'],
                    datasets: scenarios.irrScenarios
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'IRR Forecast by Scenario'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Net IRR (%)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(1) + '%';
                                }
                            }
                        }
                    }
                }
            });
            
            // Update breakdown table
            updateScenarioBreakdown();
            
            // Update investment-by-investment analysis
            updateInvestmentDetails(selectedScenario);
        }

        function updateScenarioBreakdown() {
            const totalInvested = investments.reduce((sum, inv) => sum + inv.checkSize, 0);
            const totalValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
            const winners = investments.filter(inv => inv.multiple >= 3.0);
            
            // Get simulation results
            const sims = window.scenarioSimulations || {};
            
            // Calculate called capital
            const currentDate = new Date();
            const fundStartDate = new Date(fundData.vintage, 0, 1);
            const quartersElapsed = Math.floor((currentDate - fundStartDate) / (1000 * 60 * 60 * 24 * 91.25));
            const managementFeesPerQuarter = (fundData.size * fundData.managementFee) / 4;
            const totalManagementFeesCalled = Math.min(quartersElapsed * managementFeesPerQuarter, fundData.size * fundData.managementFee * fundData.fundLife);
            const calledCapital = totalInvested + totalManagementFeesCalled;
            const remainingCapital = fundData.size - calledCapital;
            
            const scenarioData = [
                {
                    name: "🛑 Stop Investing",
                    class: "scenario-stop",
                    sim: sims.stop,
                    assumptions: {
                        newDeployment: "$0M (0% of remaining)",
                        followOnReserve: `$${(remainingCapital * 0.8 / 1e6).toFixed(0)}M (80% of remaining)`,
                        strategy: "Only follow-on existing winners",
                        graduationRate: "Existing portfolio only",
                        newDeals: "0 new investments"
                    },
                    rationale: "Conservative approach. Relies entirely on existing portfolio appreciation and follow-on opportunities in proven winners."
                },
                {
                    name: "🎯 Conservative Deployment",
                    class: "scenario-conservative",
                    sim: sims.conservative,
                    assumptions: {
                        newDeployment: `$${(remainingCapital * 0.5 / 1e6).toFixed(0)}M (50% of remaining)`,
                        followOnReserve: `$${(remainingCapital * 0.5 / 1e6).toFixed(0)}M (50% of remaining)`,
                        strategy: "Balanced new deals + follow-ons",
                        graduationRate: "50% Series B graduation rate",
                        newDeals: `${Math.floor(remainingCapital * 0.5 / 20e6)} new $20M investments`
                    },
                    rationale: "Balanced approach. Modest new deployment while maintaining strong follow-on capacity for winners."
                },
                {
                    name: "🚀 Aggressive Deployment",
                    class: "scenario-aggressive",
                    sim: sims.aggressive,
                    assumptions: {
                        newDeployment: `$${(remainingCapital * 0.8 / 1e6).toFixed(0)}M (80% of remaining)`,
                        followOnReserve: `$${(remainingCapital * 0.2 / 1e6).toFixed(0)}M (20% of remaining)`,
                        strategy: "Maximize new deal exposure",
                        graduationRate: "50% Series B graduation rate",
                        newDeals: `${Math.floor(remainingCapital * 0.8 / 20e6)} new $20M investments`
                    },
                    rationale: "High-risk, high-reward. Deploy most remaining capital into new deals. Higher upside but needs new deals to perform well."
                },
                {
                    name: "💰 Follow-On Focused",
                    class: "scenario-followon",
                    sim: sims.followon,
                    assumptions: {
                        newDeployment: `$${(remainingCapital * 0.3 / 1e6).toFixed(0)}M (30% of remaining)`,
                        followOnReserve: `$${(remainingCapital * 0.7 / 1e6).toFixed(0)}M (70% of remaining)`,
                        strategy: "Maximize follow-on deployment",
                        graduationRate: `${winners.length} winners @ 75% follow-on success`,
                        newDeals: `${Math.floor(remainingCapital * 0.3 / 20e6)} new $20M investments`
                    },
                    rationale: `Optimal for funds with ${winners.length} proven winners. Maximizes deployment into highest-probability opportunities.`
                }
            ];
            
            let tableHTML = `
                <table class="breakdown-table">
                    <thead>
                        <tr>
                            <th>Scenario</th>
                            <th>New Deployment</th>
                            <th>Follow-On Reserve</th>
                            <th>Year 1 TVPI</th>
                            <th>Year 3 TVPI</th>
                            <th>Year 5 TVPI</th>
                            <th>P25-P75 Range</th>
                            <th>Strategy Rationale</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            scenarioData.forEach(scenario => {
                const sim = scenario.sim || {};
                const year1 = sim.meanTVPI ? sim.meanTVPI[1] : 0;
                const year3 = sim.meanTVPI ? sim.meanTVPI[3] : 0;
                const year5 = sim.meanTVPI ? sim.meanTVPI[5] : 0;
                const p25 = sim.p25 || 0;
                const p75 = sim.p75 || 0;
                
                tableHTML += `
                    <tr class="${scenario.class}">
                        <td class="scenario-name">${scenario.name}</td>
                        <td>${scenario.assumptions.newDeployment}</td>
                        <td>${scenario.assumptions.followOnReserve}</td>
                        <td>${year1.toFixed(2)}x</td>
                        <td>${year3.toFixed(2)}x</td>
                        <td>${year5.toFixed(2)}x</td>
                        <td>${p25.toFixed(2)}x - ${p75.toFixed(2)}x</td>
                        <td>${scenario.rationale}</td>
                    </tr>
                `;
            });
            
            tableHTML += `
                    </tbody>
                </table>
                <div style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-radius: 8px; font-size: 0.875em; color: #5f6368;">
                    <strong>Monte Carlo Methodology:</strong> Each scenario runs 1,000 simulations starting from your ACTUAL current portfolio:<br>
                    • <strong>Baseline:</strong> Current Net TVPI ${calculateCurrentNetTVPI().toFixed(2)}x from ${investments.length} investments<br>
                    • <strong>Existing investments:</strong> Winners grow 15-40% annually, others -5% to +15%, underperformers decline<br>
                    • <strong>Follow-ons:</strong> 75% success rate in proven winners, 2x-6x returns when successful<br>
                    • <strong>New investments:</strong> Series B (50% graduation), actual VC power-law return distribution<br>
                    • <strong>Fees & Carry:</strong> Uses same quarterly management fee and carry calculations as dashboard<br>
                    <strong>P25-P75 Range:</strong> Shows confidence interval from actual portfolio projections.
                </div>
            `;
            
            document.getElementById('scenarioBreakdown').innerHTML = tableHTML;
        }

        function updateInvestmentDetails(scenarioType) {
            const sims = window.scenarioSimulations || {};
            const sim = sims[scenarioType];
            
            if (!sim) {
                document.getElementById('investmentDetails').innerHTML = '<p>Select a scenario to see detailed investment analysis.</p>';
                return;
            }
            
            const winners = investments.filter(inv => inv.multiple >= 3.0);
            
            let detailsHTML = `
                <h4 style="margin-bottom: 16px;">📊 ${scenarioType.charAt(0).toUpperCase() + scenarioType.slice(1)} Scenario Analysis</h4>
                
                <div style="margin-bottom: 24px;">
                    <h5 style="margin-bottom: 12px; color: #188038;">🏆 Existing Portfolio Companies</h5>
                    <table class="breakdown-table">
                        <thead>
                            <tr>
                                <th>Company</th>
                                <th>Current Check</th>
                                <th>Current Multiple</th>
                                <th>Follow-On Check</th>
                                <th>Projected 5Y Multiple</th>
                                <th>P25-P75 Range</th>
                                <th>Total Return</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            investments.forEach(inv => {
                const isWinner = inv.multiple >= 3.0;
                let followOnAmount = 0;
                let projectedMultiple = inv.multiple;
                let p25Multiple = inv.multiple * 0.8;
                let p75Multiple = inv.multiple * 1.2;
                
                if (isWinner && sim.followOnReserve > 0) {
                    followOnAmount = Math.min(inv.checkSize * 1.5, sim.followOnReserve / winners.length);
                    
                    // Estimate projected performance for winners
                    const growthFactor = Math.pow(1.275, 5); // 27.5% average annual growth for winners
                    projectedMultiple = inv.multiple * growthFactor;
                    p25Multiple = projectedMultiple * 0.7;
                    p75Multiple = projectedMultiple * 1.4;
                } else if (inv.multiple >= 1.0) {
                    // Modest growth for performing investments
                    const growthFactor = Math.pow(1.05, 5); // 5% average annual growth
                    projectedMultiple = inv.multiple * growthFactor;
                    p25Multiple = projectedMultiple * 0.6;
                    p75Multiple = projectedMultiple * 1.3;
                } else {
                    // Underperformers may decline
                    const declineFactor = Math.pow(0.95, 5); // -5% annual decline
                    projectedMultiple = Math.max(0.1, inv.multiple * declineFactor);
                    p25Multiple = 0.0;
                    p75Multiple = projectedMultiple * 1.5;
                }
                
                const totalReturn = (inv.checkSize * projectedMultiple) + (followOnAmount > 0 ? followOnAmount * 3.5 : 0);
                
                detailsHTML += `
                    <tr>
                        <td><strong>${inv.company}</strong></td>
                        <td>$${(inv.checkSize / 1e6).toFixed(1)}M</td>
                        <td>${inv.multiple.toFixed(1)}x</td>
                        <td>${followOnAmount > 0 ? '$' + (followOnAmount / 1e6).toFixed(1) + 'M' : '-'}</td>
                        <td>${projectedMultiple.toFixed(1)}x</td>
                        <td>${p25Multiple.toFixed(1)}x - ${p75Multiple.toFixed(1)}x</td>
                        <td>$${(totalReturn / 1e6).toFixed(1)}M</td>
                    </tr>
                `;
            });
            
            detailsHTML += `
                        </tbody>
                    </table>
                </div>
            `;
            
            // Add new investments section
            if (sim.numNewDeals > 0) {
                detailsHTML += `
                    <div style="margin-bottom: 24px;">
                        <h5 style="margin-bottom: 12px; color: #1a73e8;">🚀 New Investment Opportunities</h5>
                        <table class="breakdown-table">
                            <thead>
                                <tr>
                                    <th>Investment</th>
                                    <th>Initial Check</th>
                                    <th>Follow-On Potential</th>
                                    <th>Success Probability</th>
                                    <th>Expected Multiple</th>
                                    <th>P25-P75 Range</th>
                                    <th>Expected Return</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                for (let i = 1; i <= sim.numNewDeals; i++) {
                    const checkSize = 20; // $20M
                    const successProb = 0.50; // 50% graduation rate for Series B
                    const expectedMultiple = 2.2; // Expected return for successful Series B
                    const followOnPotential = checkSize * 0.75; // Potential follow-on
                    const expectedReturn = checkSize * expectedMultiple * successProb;
                    
                    detailsHTML += `
                        <tr>
                            <td><strong>New Deal ${i}</strong></td>
                            <td>$${checkSize}M</td>
                            <td>$${followOnPotential.toFixed(1)}M</td>
                            <td>${(successProb * 100).toFixed(0)}%</td>
                            <td>${expectedMultiple.toFixed(1)}x</td>
                            <td>0.0x - 5.0x</td>
                            <td>$${expectedReturn.toFixed(1)}M</td>
                        </tr>
                    `;
                }
                
                detailsHTML += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            // Add summary
            const totalExpectedReturn = sim.p50 * sim.newDeployment + sim.followOnReserve;
            detailsHTML += `
                <div style="padding: 16px; background: #e8f0fe; border-radius: 12px; border-left: 4px solid #1a73e8;">
                    <h5 style="margin-bottom: 8px; color: #1a73e8;">📈 Scenario Summary</h5>
                    <p><strong>Total Capital Deployed:</strong> $${((sim.newDeployment + sim.followOnReserve) / 1e6).toFixed(0)}M</p>
                    <p><strong>Expected Final TVPI:</strong> ${sim.p50.toFixed(2)}x</p>
                    <p><strong>Confidence Range:</strong> ${sim.p25.toFixed(2)}x - ${sim.p75.toFixed(2)}x</p>
                    <p><strong>Risk Profile:</strong> ${scenarioType === 'followon' ? 'Lower risk (proven winners)' : 
                                                     scenarioType === 'aggressive' ? 'Higher risk (new deal heavy)' :
                                                     scenarioType === 'stop' ? 'Lowest risk (no new capital)' :
                                                     'Balanced risk'}</p>
                </div>
            `;
            
            document.getElementById('investmentDetails').innerHTML = detailsHTML;
        }

        function generateScenarioData() {
            // Run Monte Carlo simulations for each scenario
            const simulations = {
                stop: runScenarioSimulation('stop'),
                conservative: runScenarioSimulation('conservative'),
                aggressive: runScenarioSimulation('aggressive'),
                followon: runScenarioSimulation('followon')
            };
            
            const scenarios = {
                stop: {
                    label: 'Stop Investing',
                    data: simulations.stop.meanTVPI,
                    irrData: simulations.stop.meanIRR,
                    borderColor: '#ea4335',
                    backgroundColor: 'rgba(234, 67, 53, 0.1)'
                },
                conservative: {
                    label: 'Conservative',
                    data: simulations.conservative.meanTVPI,
                    irrData: simulations.conservative.meanIRR,
                    borderColor: '#fbbc04',
                    backgroundColor: 'rgba(251, 188, 4, 0.1)'
                },
                aggressive: {
                    label: 'Aggressive',
                    data: simulations.aggressive.meanTVPI,
                    irrData: simulations.aggressive.meanIRR,
                    borderColor: '#34a853',
                    backgroundColor: 'rgba(52, 168, 83, 0.1)'
                },
                followon: {
                    label: 'Follow-On Focused',
                    data: simulations.followon.meanTVPI,
                    irrData: simulations.followon.meanIRR,
                    borderColor: '#1a73e8',
                    backgroundColor: 'rgba(26, 115, 232, 0.1)'
                }
            };
            
            // Store simulation results for breakdown table
            window.scenarioSimulations = simulations;
            
            return {
                tvpiScenarios: Object.values(scenarios).map(scenario => ({
                    label: scenario.label,
                    data: scenario.data,
                    borderColor: scenario.borderColor,
                    backgroundColor: scenario.backgroundColor,
                    tension: 0.4,
                    fill: false
                })),
                irrScenarios: Object.values(scenarios).map(scenario => ({
                    label: scenario.label,
                    data: scenario.irrData.map(irr => irr * 100), // Convert to percentage
                    borderColor: scenario.borderColor,
                    backgroundColor: scenario.backgroundColor,
                    tension: 0.4,
                    fill: false
                }))
            };
        }

        function runScenarioSimulation(scenarioType) {
            const NUM_SIMULATIONS = 1000;
            
            // Use ACTUAL current portfolio calculations
            const totalInvested = investments.reduce((sum, inv) => sum + inv.checkSize, 0);
            const currentTotalValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
            
            // Calculate called capital using SAME logic as performance dashboard
            const currentDate = new Date();
            const fundStartDate = new Date(fundData.vintage, 0, 1);
            const quartersElapsed = Math.floor((currentDate - fundStartDate) / (1000 * 60 * 60 * 24 * 91.25));
            const managementFeesPerQuarter = (fundData.size * fundData.managementFee) / 4;
            const totalManagementFeesCalled = Math.min(quartersElapsed * managementFeesPerQuarter, fundData.size * fundData.managementFee * fundData.fundLife);
            const calledCapital = totalInvested + totalManagementFeesCalled;
            const remainingCapital = fundData.size - calledCapital;
            
            // Calculate ACTUAL current Net TVPI using same logic as dashboard
            const currentGrossTVPI = currentTotalValue / calledCapital;
            const currentTotalProfits = Math.max(0, currentTotalValue - calledCapital);
            const currentCarryOnProfits = currentTotalProfits * fundData.carry;
            const currentNetValue = currentTotalValue - currentCarryOnProfits;
            const currentNetTVPI = currentNetValue / calledCapital;
            
            // Define scenario parameters
            const scenarioParams = {
                stop: { newDeploymentRate: 0.0, followOnRate: 0.8 },
                conservative: { newDeploymentRate: 0.5, followOnRate: 0.5 },
                aggressive: { newDeploymentRate: 0.8, followOnRate: 0.2 },
                followon: { newDeploymentRate: 0.3, followOnRate: 0.7 }
            };
            
            const params = scenarioParams[scenarioType];
            const newDeployment = remainingCapital * params.newDeploymentRate;
            const followOnReserve = remainingCapital * params.followOnRate;
            
            let allSimResults = [];
            let allInvestmentResults = {}; // Track individual investment outcomes
            
            // Initialize tracking for existing investments
            investments.forEach(inv => {
                allInvestmentResults[inv.company] = {
                    type: 'existing',
                    originalCheck: inv.checkSize,
                    currentValue: inv.currentValue,
                    currentMultiple: inv.multiple,
                    isWinner: inv.multiple >= 3.0,
                    finalValues: [],
                    followOnAmounts: [],
                    totalInvested: [],
                    finalMOICs: []
                };
            });
            
            // Initialize tracking for new investments
            const numNewDeals = Math.floor(newDeployment / 20e6);
            for (let i = 1; i <= numNewDeals; i++) {
                allInvestmentResults[`New Deal ${i}`] = {
                    type: 'new',
                    originalCheck: 20e6,
                    finalValues: [],
                    followOnAmounts: [],
                    totalInvested: [],
                    finalMOICs: []
                };
            }
            
            for (let sim = 0; sim < NUM_SIMULATIONS; sim++) {
                // Start with ACTUAL current portfolio value
                let totalValue = currentTotalValue;
                let totalCalled = calledCapital;
                
                // Project existing investments forward 5 years using ACTUAL current multiples
                investments.forEach(inv => {
                    const isWinner = inv.multiple >= 3.0;
                    
                    // Remove current value, add projected value
                    totalValue -= inv.currentValue;
                    
                    // Project this investment forward based on its ACTUAL current performance
                    let projectedValue;
                    if (isWinner) {
                        // Winners continue growing but at slower rate
                        const growthRate = 0.15 + (Math.random() * 0.25); // 15-40% annual growth
                        projectedValue = inv.currentValue * Math.pow(1 + growthRate, 5);
                    } else if (inv.multiple >= 1.0) {
                        // Performing investments may grow modestly or stagnate
                        const growthRate = -0.05 + (Math.random() * 0.20); // -5% to +15% annual
                        projectedValue = inv.currentValue * Math.pow(1 + growthRate, 5);
                    } else {
                        // Underperforming investments likely stay flat or decline
                        const growthRate = -0.10 + (Math.random() * 0.10); // -10% to 0% annual
                        projectedValue = Math.max(0, inv.currentValue * Math.pow(1 + growthRate, 5));
                    }
                    
                    totalValue += projectedValue;
                    
                    // Model follow-on investments in winners
                    if (isWinner && followOnReserve > 0) {
                        const winners = investments.filter(i => i.multiple >= 3.0);
                        const followOnAmount = Math.min(inv.checkSize * 1.5, followOnReserve / winners.length);
                        
                        if (Math.random() < 0.75) { // 75% chance of successful follow-on
                            // Follow-on performs better than new investments
                            const followOnMultiple = 2.0 + (Math.random() * 4.0); // 2x to 6x return
                            const followOnValue = followOnAmount * followOnMultiple;
                            totalValue += followOnValue;
                            totalCalled += followOnAmount;
                        } else {
                            // Follow-on fails
                            totalValue += followOnAmount * (Math.random() * 0.5); // 0-50% return
                            totalCalled += followOnAmount;
                        }
                    }
                });
                
                // Simulate new investments
                if (newDeployment > 0) {
                    const avgCheckSize = 20e6; // $20M average for Series B
                    const numNewDeals = Math.floor(newDeployment / avgCheckSize);
                    
                    for (let deal = 0; deal < numNewDeals; deal++) {
                        // Use actual VC return distribution for Series B
                        if (Math.random() < 0.50) { // 50% graduation rate
                            // Successful Series B returns
                            const returnMultiples = [0.2, 1.1, 0.6, 2.8, 10.0, 0.06]; // From STAGE_PARAMS
                            const randomReturn = returnMultiples[Math.floor(Math.random() * returnMultiples.length)];
                            totalValue += avgCheckSize * randomReturn;
                        } else {
                            // Failed investment
                            totalValue += avgCheckSize * (Math.random() * 0.3); // 0-30% return
                        }
                        totalCalled += avgCheckSize;
                    }
                }
                
                // Add management fees for remaining 5 years (quarterly)
                const additionalQuarters = 20; // 5 years * 4 quarters
                const additionalMgmtFees = Math.min(additionalQuarters * managementFeesPerQuarter, 
                                                  fundData.size * fundData.managementFee * fundData.fundLife - totalManagementFeesCalled);
                totalCalled += additionalMgmtFees;
                
                // Calculate net TVPI (after carry)
                const totalProfits = Math.max(0, totalValue - totalCalled);
                const carryOnProfits = totalProfits * fundData.carry;
                const netValue = totalValue - carryOnProfits;
                const netTVPI = netValue / totalCalled;
                
                // Calculate IRR for this simulation
                const currentYear = new Date().getFullYear();
                const vintageYear = fundData.vintage;
                const yearsElapsed = currentYear - vintageYear;
                
                // Simple IRR calculation: (Final Value / Called Capital) ^ (1/years) - 1
                const finalIRR = Math.pow(netTVPI, 1/(yearsElapsed + 5)) - 1; // +5 for full fund life
                const year1IRR = yearsElapsed >= 1 ? Math.pow(netTVPI * 0.7, 1/(yearsElapsed + 1)) - 1 : 0;
                const year3IRR = Math.pow(netTVPI * 0.85, 1/(yearsElapsed + 3)) - 1;
                const year5IRR = Math.pow(netTVPI, 1/(yearsElapsed + 5)) - 1;
                
                allSimResults.push({
                    totalValue,
                    totalCalled,
                    netTVPI,
                    year1: netTVPI * 0.7,
                    year3: netTVPI * 0.85,
                    year5: netTVPI,
                    finalIRR,
                    year1IRR,
                    year3IRR,
                    year5IRR
                });
            }
            
            // Calculate statistics - start with ACTUAL current Net TVPI
            const meanTVPI = [
                currentNetTVPI, // Use actual current Net TVPI calculated above
                allSimResults.reduce((sum, r) => sum + r.year1, 0) / NUM_SIMULATIONS,
                allSimResults.reduce((sum, r) => sum + r.year3, 0) / NUM_SIMULATIONS * 0.9,
                allSimResults.reduce((sum, r) => sum + r.year3, 0) / NUM_SIMULATIONS,
                allSimResults.reduce((sum, r) => sum + r.year5, 0) / NUM_SIMULATIONS * 0.95,
                allSimResults.reduce((sum, r) => sum + r.year5, 0) / NUM_SIMULATIONS
            ];
            
            // Calculate current IRR based on actual fund vintage and current Net TVPI
            const currentYear = new Date().getFullYear();
            const yearsElapsed = currentYear - fundData.vintage;
            const currentIRR = yearsElapsed > 0 ? Math.pow(currentNetTVPI, 1/yearsElapsed) - 1 : 0;
            
            const meanIRR = [
                currentIRR, // Use actual current IRR
                allSimResults.reduce((sum, r) => sum + r.year1IRR, 0) / NUM_SIMULATIONS,
                allSimResults.reduce((sum, r) => sum + r.year3IRR, 0) / NUM_SIMULATIONS * 0.9,
                allSimResults.reduce((sum, r) => sum + r.year3IRR, 0) / NUM_SIMULATIONS,
                allSimResults.reduce((sum, r) => sum + r.year5IRR, 0) / NUM_SIMULATIONS * 0.95,
                allSimResults.reduce((sum, r) => sum + r.year5IRR, 0) / NUM_SIMULATIONS
            ];
            
            return {
                meanTVPI,
                meanIRR,
                p25: allSimResults.map(r => r.netTVPI).sort((a,b) => a-b)[Math.floor(NUM_SIMULATIONS * 0.25)],
                p50: allSimResults.map(r => r.netTVPI).sort((a,b) => a-b)[Math.floor(NUM_SIMULATIONS * 0.5)],
                p75: allSimResults.map(r => r.netTVPI).sort((a,b) => a-b)[Math.floor(NUM_SIMULATIONS * 0.75)],
                finalIRR: allSimResults.reduce((sum, r) => sum + r.finalIRR, 0) / NUM_SIMULATIONS,
                newDeployment,
                followOnReserve,
                numNewDeals: Math.floor(newDeployment / 20e6)
            };
        }



                 function calculateCurrentNetTVPI() {
             const totalInvested = investments.reduce((sum, inv) => sum + inv.checkSize, 0);
             const totalValue = investments.reduce((sum, inv) => sum + inv.currentValue, 0);
             
             // Calculate management fees called (quarterly)
             const currentDate = new Date();
             const fundStartDate = new Date(fundData.vintage, 0, 1);
             const quartersElapsed = Math.floor((currentDate - fundStartDate) / (1000 * 60 * 60 * 24 * 91.25));
             const managementFeesPerQuarter = (fundData.size * fundData.managementFee) / 4;
             const totalManagementFeesCalled = Math.min(quartersElapsed * managementFeesPerQuarter, fundData.size * fundData.managementFee * fundData.fundLife);
             
             // Called capital = invested + management fees
             const calledCapital = totalInvested + totalManagementFeesCalled;
             
             // Net calculations (carry only on profits above called capital)
             const totalProfits = Math.max(0, totalValue - calledCapital);
             const carryOnProfits = totalProfits * fundData.carry;
             const netValue = totalValue - carryOnProfits;
             
             return netValue / calledCapital;
         }

        function generateRecommendations() {
            const container = document.getElementById('recommendations');
            
            const totalInvested = investments.reduce((sum, inv) => sum + inv.checkSize, 0);
            const remainingCapital = fundData.size - totalInvested;
            const winners = investments.filter(inv => inv.multiple >= 3.0);
            const deploymentRate = (totalInvested / fundData.size) * 100;
            
            let recommendations = `
                <div class="card" style="background: #e8f0fe; border: 2px solid #1a73e8;">
                    <h3 style="color: #1a73e8; margin-bottom: 16px;">🎯 Strategic Recommendations</h3>
                    
                    <div style="margin-bottom: 24px;">
                        <h4 style="color: #1f1f1f; margin-bottom: 12px;">Portfolio Status</h4>
                        <p>• <strong>Deployed:</strong> $${(totalInvested / 1e6).toFixed(0)}M (${deploymentRate.toFixed(1)}% of fund)</p>
                        <p>• <strong>Remaining:</strong> $${(remainingCapital / 1e6).toFixed(0)}M available for deployment</p>
                        <p>• <strong>Winners:</strong> ${winners.length} companies at 3x+ returns</p>
                    </div>
            `;
            
            if (winners.length > 0) {
                const totalWinnerValue = winners.reduce((sum, inv) => sum + inv.currentValue, 0);
                const totalWinnerCost = winners.reduce((sum, inv) => sum + inv.checkSize, 0);
                
                recommendations += `
                    <div style="margin-bottom: 24px;">
                        <h4 style="color: #188038; margin-bottom: 12px;">🏆 Follow-On Opportunities</h4>
                        <p>Your ${winners.length} winners represent $${(totalWinnerValue / 1e6).toFixed(0)}M in current value from $${(totalWinnerCost / 1e6).toFixed(0)}M invested.</p>
                        <p><strong>Recommended follow-on allocation:</strong> $${Math.min(100, remainingCapital * 0.6 / 1e6).toFixed(0)}M (60% of remaining capital)</p>
                        <ul style="margin-left: 20px; margin-top: 12px;">
                `;
                
                winners.forEach(winner => {
                    const suggestedFollowOn = Math.min(winner.checkSize * 1.5, remainingCapital * 0.2);
                    recommendations += `<li><strong>${winner.company}:</strong> Consider $${(suggestedFollowOn / 1e6).toFixed(0)}M follow-on (${winner.multiple.toFixed(1)}x current)</li>`;
                });
                
                recommendations += `</ul></div>`;
            }
            
            recommendations += `
                    <div style="margin-bottom: 24px;">
                        <h4 style="color: #1f1f1f; margin-bottom: 12px;">💡 New Investment Strategy</h4>
                        <p><strong>Recommended new deal allocation:</strong> $${Math.min(80, remainingCapital * 0.4 / 1e6).toFixed(0)}M (40% of remaining capital)</p>
                        <p>• Focus on ${deploymentRate < 60 ? 'Series B' : 'Series C'} opportunities where you've had success</p>
                        <p>• Target check sizes of $15-25M to maintain portfolio concentration</p>
                        <p>• Prioritize sectors/stages matching your winner profile</p>
                    </div>
                    
                    <div style="background: rgba(255, 255, 255, 0.8); padding: 16px; border-radius: 12px; border: 1px solid #1a73e8;">
                        <h4 style="color: #1a73e8; margin-bottom: 8px;">📊 Optimal Strategy</h4>
                        <p><strong>${winners.length > 2 ? 'Follow-On Focused' : 'Balanced Deployment'}</strong></p>
                        <p>${winners.length > 2 ? 
                            'With multiple proven winners, maximize follow-on capital to capture outsized returns from your best performers.' : 
                            'Balance new investments with follow-on reserves to diversify while supporting emerging winners.'
                        }</p>
                    </div>
                </div>
            `;
            
            container.innerHTML = recommendations;
        }

        function exportReport() {
            // Generate a comprehensive report
            const report = {
                fund: fundData,
                investments: investments,
                timestamp: new Date().toISOString(),
                performance: calculateCurrentNetTVPI()
            };
            
            // For now, just log to console
            console.log('Portfolio Report:', report);
            alert('Report generated! Check console for data (CSV export coming soon)');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            
            // Initialize investment details as empty
            document.getElementById('investmentDetails').innerHTML = '<p style="color: #5f6368; font-style: italic;">Select a scenario above to see detailed investment-by-investment analysis.</p>';
        });
    </script>
</body>
</html> 