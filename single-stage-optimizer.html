<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Single-Stage Fund Optimizer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 0;
            margin: 0;
            color: #1f1f1f;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            color: #1f1f1f;
            margin-bottom: 8px;
            font-size: 2.5em;
            font-weight: 400;
            letter-spacing: -0.5px;
        }

        .subtitle {
            text-align: center;
            color: #5f6368;
            margin-bottom: 32px;
            font-size: 1.1em;
            font-weight: 400;
        }

        .card {
            background: #ffffff;
            border-radius: 24px;
            padding: 24px;
            margin-bottom: 16px;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 1px 3px 1px rgba(60,64,67,0.15);
            border: 1px solid #e8eaed;
        }

        .stage-selector {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .stage-option {
            padding: 16px 24px;
            border: 1px solid #dadce0;
            border-radius: 16px;
            background: #ffffff;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            min-width: 120px;
        }

        .stage-option:hover {
            background: #f8f9fa;
            border-color: #1a73e8;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 2px 6px 2px rgba(60,64,67,0.15);
        }

        .stage-option.selected {
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
        }

        .stage-title {
            font-weight: 600;
            font-size: 1.1em;
        }

        .stage-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .stage-option.selected .stage-details {
            color: rgba(255, 255, 255, 0.8);
        }

        .mini-chart-container {
            margin-top: 12px;
            height: 65px;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.05);
            padding: 6px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stage-option.selected .mini-chart-container {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .stage-option:hover .mini-chart-container {
            transform: translateY(-1px);
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
        }

        .params-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .param-section {
            background: #f8f9fa;
            padding: 24px;
            border-radius: 16px;
            border: 1px solid #e8eaed;
        }

        .param-section h3 {
            margin-bottom: 16px;
            color: #1f1f1f;
            font-weight: 500;
            font-size: 1.1em;
            letter-spacing: 0.15px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #34495e;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: #ffffff;
            font-family: 'Roboto', sans-serif;
        }

        .input-group input:hover, .input-group select:hover {
            border-color: #1f1f1f;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #1a73e8;
            border-width: 2px;
            padding: 11px 15px;
        }

        .slider-group {
            margin-bottom: 20px;
        }

        .slider-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #34495e;
        }

        .slider-container {
            position: relative;
            margin-bottom: 10px;
        }

        .slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #e0e0e0;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3498db;
            cursor: pointer;
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3498db;
            cursor: pointer;
            border: none;
        }

        .slider-value {
            text-align: center;
            font-weight: 600;
            color: #3498db;
            margin-top: 5px;
        }

        .follow-on-strategies {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .strategy-card {
            border: 1px solid #dadce0;
            border-radius: 12px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            background: #ffffff;
        }

        .strategy-card:hover {
            background: #f8f9fa;
            border-color: #1a73e8;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 1px 3px 1px rgba(60,64,67,0.15);
        }

        .strategy-card.selected {
            background: #e8f0fe;
            border-color: #1a73e8;
            color: #1a73e8;
        }

        .strategy-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .strategy-description {
            font-size: 0.9em;
            color: #666;
        }

        .run-button {
            width: 100%;
            padding: 16px 24px;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 100px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            letter-spacing: 0.25px;
            font-family: 'Google Sans', 'Roboto', sans-serif;
        }

        .run-button:hover {
            background: #1765cc;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 2px 6px 2px rgba(60,64,67,0.15);
        }
        
        .run-button:active {
            background: #1557b0;
        }

        .results {
            display: none;
            margin-top: 30px;
        }

        .results.show {
            display: block;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: #ffffff;
            padding: 24px;
            border-radius: 16px;
            text-align: center;
            border: 1px solid #e8eaed;
            box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 1px 3px 1px rgba(60,64,67,0.15);
        }

        .metric-value {
            font-size: 2em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #7f8c8d;
            font-weight: 500;
        }

        .chart-container {
            height: 400px;
            margin-bottom: 30px;
        }

        .insights {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 10px;
            padding: 20px;
        }

        .insights h3 {
            color: #2e7d32;
            margin-bottom: 15px;
        }

        .insight-item {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }

        .insight-item:before {
            content: "•";
            color: #4caf50;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                max-width: 100%;
            }
            
            .card {
                padding: 15px;
                margin-bottom: 15px;
            }
            
            h1 {
                font-size: 1.8em;
            }
            
            .params-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .stage-selector {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }
            
            .stage-option {
                width: 100%;
                max-width: none;
            }
            
            .follow-on-strategies {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .metric-card {
                padding: 15px;
            }
            
            .metric-value {
                font-size: 1.5em;
            }
            
            .chart-container {
                height: 300px;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 1.5em;
            }
            
            .subtitle {
                font-size: 0.9em;
            }
            
            .run-button {
                font-size: 1em;
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Single-Stage Fund Optimizer</h1>
        <p class="subtitle">Master the trade-offs: Portfolio size vs. Follow-on reserves vs. Strategy selection</p>

        <div class="card">
            <h2 style="margin-bottom: 20px; text-align: center;">1. Select Your Investment Stage</h2>
            <div class="stage-selector">
                <div class="stage-option" data-stage="Seed">
                    <div class="stage-title">Seed</div>
                    <div class="stage-details">$1M - $5M<br>High risk, high reward</div>
                    <div class="mini-chart-container">
                        <canvas id="seedChart" width="110" height="55"></canvas>
                    </div>
                </div>
                <div class="stage-option" data-stage="SeriesA">
                    <div class="stage-title">Series A</div>
                    <div class="stage-details">$7M - $15M<br>Product-market fit</div>
                    <div class="mini-chart-container">
                        <canvas id="seriesAChart" width="110" height="55"></canvas>
                    </div>
                </div>
                <div class="stage-option selected" data-stage="SeriesB">
                    <div class="stage-title">Series B</div>
                    <div class="stage-details">$10M - $25M<br>Scaling operations</div>
                    <div class="mini-chart-container">
                        <canvas id="seriesBChart" width="110" height="55"></canvas>
                    </div>
                </div>
                <div class="stage-option" data-stage="SeriesC">
                    <div class="stage-title">Series C</div>
                    <div class="stage-details">$15M - $40M<br>Market expansion</div>
                    <div class="mini-chart-container">
                        <canvas id="seriesCChart" width="110" height="55"></canvas>
                    </div>
                </div>
                <div class="stage-option" data-stage="Growth">
                    <div class="stage-title">Growth</div>
                    <div class="stage-details">$30M - $50M<br>Pre-IPO scaling</div>
                    <div class="mini-chart-container">
                        <canvas id="growthChart" width="110" height="55"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2 style="margin-bottom: 20px;">2. Configure Fund Parameters</h2>
            <div class="params-grid">
                <div class="param-section">
                    <h3>Fund Structure</h3>
                    <div class="input-group">
                        <label for="fundSize">Fund Size ($M)</label>
                        <input type="number" id="fundSize" value="400" min="50" max="2000" step="25">
                    </div>
                    <div class="input-group">
                        <label for="numTrials">Monte Carlo Trials</label>
                        <input type="number" id="numTrials" value="5000" min="1000" max="10000" step="1000">
                    </div>
                    <div class="input-group">
                        <label for="targetNetTVPI">Target Net TVPI</label>
                        <input type="number" id="targetNetTVPI" value="2.5" min="1.5" max="5.0" step="0.1">
                    </div>
                </div>

                <div class="param-section">
                    <h3>Optimization Matrix</h3>
                    <div class="input-group">
                        <label>Constrained Portfolio Sizes</label>
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                            <strong>Testing: 5-22 companies (fund size permitting)</strong>
                            <div style="font-size: 0.9em; color: #666; margin-top: 5px;">
                                Curves show each strategy's performance across portfolio sizes
                            </div>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <label>Derived Follow-On Reserves</label>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                            <strong>Calculated: Based on remaining capital after initial deployment</strong>
                            <div style="font-size: 0.9em; color: #666; margin-top: 5px;">
                                Ensures mathematical consistency between variables
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2 style="margin-bottom: 20px;">3. Select Follow-On Strategy</h2>
            <div class="follow-on-strategies">
                <div class="strategy-card selected" data-strategy="equal">
                    <div class="strategy-title">Equal Allocation</div>
                    <div class="strategy-description">Spread follow-on capital equally across all companies that raise next round</div>
                </div>
                <div class="strategy-card" data-strategy="performance">
                    <div class="strategy-title">Performance-Based</div>
                    <div class="strategy-description">Concentrate follow-ons in top-performing companies only</div>
                </div>
                <div class="strategy-card" data-strategy="aggressive">
                    <div class="strategy-title">Aggressive Winners</div>
                    <div class="strategy-description">Maximum allocation to clear winners, minimal to others</div>
                </div>
                <div class="strategy-card" data-strategy="selective">
                    <div class="strategy-title">Highly Selective</div>
                    <div class="strategy-description">Only follow-on in top 20% performers, let others go</div>
                </div>
            </div>
        </div>

        <div class="card">
            <button class="run-button" onclick="runSingleStageOptimization()">
                🎯 Optimize Portfolio Size & Follow-On Strategy
            </button>
        </div>

        <div class="results" id="results">
            <div class="card">
                <h2 style="margin-bottom: 20px;">Optimization Results</h2>
                <div class="results-grid" id="resultsGrid">
                    <!-- Results will be populated here -->
                </div>
            </div>

            <div class="card">
                <h3 style="margin-bottom: 15px;">Performance Analysis</h3>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>

            <div class="card">
                <div class="insights" id="insights">
                    <!-- Insights will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedStage = 'SeriesB';
        let selectedStrategy = 'equal';
        let performanceChart = null;

        // Stage parameters
        const STAGE_PARAMS = {
            "Seed": {
                checkSizes: { min: 1_000_000, max: 5_000_000, avg: 3_000_000 },
                returns: [0.65, 0.70, 1.00, 2.3, 10.0, 0.15],
                nextStage: "SeriesA",
                headroom: 50
            },
            "SeriesA": {
                checkSizes: { min: 7_000_000, max: 15_000_000, avg: 11_000_000 },
                returns: [0.35, 0.90, 0.80, 2.5, 10.0, 0.10],
                nextStage: "SeriesB",
                headroom: 20
            },
            "SeriesB": {
                checkSizes: { min: 10_000_000, max: 25_000_000, avg: 17_500_000 },
                returns: [0.20, 1.10, 0.60, 2.8, 10.0, 0.06],
                nextStage: "SeriesC",
                headroom: 8
            },
            "SeriesC": {
                checkSizes: { min: 15_000_000, max: 40_000_000, avg: 27_500_000 },
                returns: [0.10, 1.00, 0.60, 2.5, 8.0, 0.08],
                nextStage: "Growth",
                headroom: 3
            },
            "Growth": {
                checkSizes: { min: 30_000_000, max: 50_000_000, avg: 40_000_000 },
                returns: [0.05, 0.95, 0.45, 2.8, 7.0, 0.05],
                nextStage: "Growth",
                headroom: 1.5
            }
        };

        // Follow-on strategies
        const FOLLOW_ON_STRATEGIES = {
            equal: {
                name: "Equal Allocation",
                getFollowOnAmount: (companies, totalReserve) => {
                    const graduating = companies.filter(c => c.willGraduate);
                    const perCompany = graduating.length > 0 ? totalReserve / graduating.length : 0;
                    return companies.map(c => c.willGraduate ? perCompany : 0);
                }
            },
            performance: {
                name: "Performance-Based", 
                getFollowOnAmount: (companies, totalReserve) => {
                    const allocations = companies.map(c => {
                        if (!c.willGraduate) return 0;
                        if (c.multiple >= 3.0) return c.initialAmount * 2.0;
                        if (c.multiple >= 2.0) return c.initialAmount * 1.5;
                        if (c.multiple >= 1.5) return c.initialAmount * 1.0;
                        if (c.multiple >= 1.0) return c.initialAmount * 0.5;
                        return c.initialAmount * 0.2;
                    });
                    
                    const totalNeeded = allocations.reduce((sum, amt) => sum + amt, 0);
                    const scaleFactor = totalNeeded > 0 ? Math.min(1, totalReserve / totalNeeded) : 0;
                    return allocations.map(amt => amt * scaleFactor);
                }
            },
            aggressive: {
                name: "Aggressive Winners",
                getFollowOnAmount: (companies, totalReserve) => {
                    const winners = companies.filter(c => c.willGraduate && c.multiple >= 2.5);
                    const others = companies.filter(c => c.willGraduate && c.multiple < 2.5);
                    
                    const winnerAllocation = totalReserve * 0.85;
                    const otherAllocation = totalReserve * 0.15;
                    
                    return companies.map(c => {
                        if (!c.willGraduate) return 0;
                        if (c.multiple >= 2.5) {
                            return winners.length > 0 ? winnerAllocation / winners.length : 0;
                        } else {
                            return others.length > 0 ? otherAllocation / others.length : 0;
                        }
                    });
                }
            },
            selective: {
                name: "Highly Selective",
                getFollowOnAmount: (companies, totalReserve) => {
                    const sorted = companies.filter(c => c.willGraduate)
                                            .sort((a, b) => b.multiple - a.multiple);
                    const topTier = sorted.slice(0, Math.ceil(sorted.length * 0.2));
                    const topTierIds = new Set(topTier.map(c => c.id));
                    
                    return companies.map(c => {
                        if (!c.willGraduate || !topTierIds.has(c.id)) return 0;
                        return totalReserve / topTier.length;
                    });
                }
            }
        };

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Stage selection
            document.querySelectorAll('.stage-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.stage-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedStage = this.dataset.stage;
                    updateEstimates();
                    updateMiniChartHighlights();
                });
            });

            // Strategy selection
            document.querySelectorAll('.strategy-card').forEach(card => {
                card.addEventListener('click', function() {
                    document.querySelectorAll('.strategy-card').forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedStrategy = this.dataset.strategy;
                });
            });

            // Fund size updates
            document.getElementById('fundSize').addEventListener('input', updateEstimates);

            updateEstimates();
            createStageMiniCharts();
        });

        function updateEstimates() {
            // Function reserved for future fund size change updates
            // Portfolio size and follow-on reserves are now optimization variables, not user inputs
        }

        let stageMiniCharts = {};

        function createStageMiniCharts() {
            const stages = ['Seed', 'SeriesA', 'SeriesB', 'SeriesC', 'Growth'];
            const chartIds = ['seedChart', 'seriesAChart', 'seriesBChart', 'seriesCChart', 'growthChart'];
            
            // Stage-specific parameters to show true tail characteristics
            const stageChartParams = {
                'Seed': { maxReturn: 25, buckets: 15, tailLabel: '25x+' },
                'SeriesA': { maxReturn: 15, buckets: 15, tailLabel: '15x+' },
                'SeriesB': { maxReturn: 10, buckets: 15, tailLabel: '10x+' },
                'SeriesC': { maxReturn: 8, buckets: 15, tailLabel: '8x+' },
                'Growth': { maxReturn: 6, buckets: 15, tailLabel: '6x+' }
            };
            
            stages.forEach((stage, index) => {
                const ctx = document.getElementById(chartIds[index]);
                if (!ctx) return;
                
                const params = stageChartParams[stage];
                
                // Generate sample return distribution for this stage
                const samples = [];
                for (let i = 0; i < 2000; i++) {
                    samples.push(drawMultiple(stage));
                }
                
                // Create histogram data with stage-appropriate scaling
                const buckets = params.buckets;
                const maxReturn = params.maxReturn;
                const bucketSize = maxReturn / buckets;
                const histogram = new Array(buckets).fill(0);
                let tailCount = 0;
                
                samples.forEach(sample => {
                    if (sample <= maxReturn) {
                        const bucketIndex = Math.min(Math.floor(sample / bucketSize), buckets - 1);
                        histogram[bucketIndex]++;
                    } else {
                        tailCount++; // Count returns beyond our max
                    }
                });
                
                // Add tail bucket for very high returns
                histogram[buckets - 1] += tailCount;
                
                // Normalize to percentages
                const total = samples.length;
                const percentages = histogram.map(count => (count / total) * 100);
                
                // Create labels with tail indicator
                const labels = histogram.map((_, i) => {
                    if (i === buckets - 1) {
                        return params.tailLabel;
                    }
                    return `${(i * bucketSize).toFixed(1)}x`;
                });
                
                const chart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: percentages,
                            backgroundColor: percentages.map((_, i) => {
                                const isSelected = stage === selectedStage;
                                const isTail = i === buckets - 1;
                                if (isTail && percentages[i] > 0.5) {
                                    // Highlight significant tail
                                    return isSelected ? 'rgba(231, 76, 60, 0.8)' : 'rgba(231, 76, 60, 0.5)';
                                }
                                return isSelected ? 'rgba(52, 152, 219, 0.8)' : 'rgba(149, 165, 166, 0.5)';
                            }),
                            borderColor: stage === selectedStage ? 'rgba(52, 152, 219, 1)' : 'rgba(127, 140, 141, 0.8)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false },
                            title: { 
                                display: true, 
                                text: 'Return Distribution',
                                font: { size: 9 },
                                color: stage === selectedStage ? '#3498db' : '#7f8c8d'
                            }
                        },
                        scales: {
                            x: {
                                display: false
                            },
                            y: {
                                display: false,
                                beginAtZero: true,
                                max: Math.max(...percentages) * 1.2
                            }
                        },
                        elements: {
                            bar: {
                                borderSkipped: false
                            }
                        }
                    }
                });
                
                stageMiniCharts[stage] = chart;
            });
        }

        function updateMiniChartHighlights() {
            Object.entries(stageMiniCharts).forEach(([stage, chart]) => {
                const isSelected = stage === selectedStage;
                chart.data.datasets[0].backgroundColor = isSelected ? 'rgba(52, 152, 219, 0.8)' : 'rgba(149, 165, 166, 0.5)';
                chart.data.datasets[0].borderColor = isSelected ? 'rgba(52, 152, 219, 1)' : 'rgba(127, 140, 141, 0.8)';
                chart.options.plugins.title.color = isSelected ? '#3498db' : '#7f8c8d';
                chart.update('none');
            });
        }

        // Statistical functions
        function getRandomCheckSize(stage) {
            const range = STAGE_PARAMS[stage].checkSizes;
            return range.min + Math.random() * (range.max - range.min);
        }

        function normalCDF(x, mean, stdDev) {
            const z = (x - mean) / stdDev;
            const t = 1 / (1 + 0.2316419 * Math.abs(z));
            const d = 0.3989423 * Math.exp(-z * z / 2);
            const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
            return z > 0 ? 1 - prob : prob;
        }

        function normalPPF(p, mean, stdDev) {
            if (p <= 0) return -Infinity;
            if (p >= 1) return Infinity;
            
            const a1 = -3.969683028665376e+01;
            const a2 = 2.209460984245205e+02;
            const a3 = -2.759285104469687e+02;
            const a4 = 1.383577518672690e+02;
            const a5 = -3.066479806614716e+01;
            const a6 = 2.506628277459239e+00;
            
            const b1 = -5.447609879822406e+01;
            const b2 = 1.615858368580409e+02;
            const b3 = -1.556989798598866e+02;
            const b4 = 6.680131188771972e+01;
            const b5 = -1.328068155288572e+01;
            
            const c1 = -7.784894002430293e-03;
            const c2 = -3.223964580411365e-01;
            const c3 = -2.400758277161838e+00;
            const c4 = -2.549732539343734e+00;
            const c5 = 4.374664141464968e+00;
            const c6 = 2.938163982698783e+00;
            
            const d1 = 7.784695709041462e-03;
            const d2 = 3.224671290700398e-01;
            const d3 = 2.445134137142996e+00;
            const d4 = 3.754408661907416e+00;
            
            let q, r;
            
            if (p < 0.02425) {
                q = Math.sqrt(-2 * Math.log(p));
                return mean + stdDev * (((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / 
                       ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
            } else if (p < 0.97575) {
                q = p - 0.5;
                r = q * q;
                return mean + stdDev * (((((a1 * r + a2) * r + a3) * r + a4) * r + a5) * r + a6) * q / 
                       (((((b1 * r + b2) * r + b3) * r + b4) * r + b5) * r + 1);
            } else {
                q = Math.sqrt(-2 * Math.log(1 - p));
                return mean + stdDev * -(((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / 
                       ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
            }
        }

        function drawMultiple(stage) {
            const [p0, mu, sigma, alpha, xmin, tailWeight] = STAGE_PARAMS[stage].returns;
            
            const randP0 = Math.random();
            if (randP0 < p0) return 0;
            
            const randTail = Math.random();
            if (randTail < tailWeight) {
                const u = Math.random();
                return xmin * Math.pow(1 - u, -1 / alpha);
            } else {
                const logXmin = Math.log(xmin);
                const FLogXmin = normalCDF(logXmin, mu, sigma);
                const u = Math.random();
                const targetCDF = u * FLogXmin;
                const logMultiple = normalPPF(targetCDF, mu, sigma);
                return Math.min(Math.exp(logMultiple), xmin * (1 - 1e-10));
            }
        }

        function getGraduationProbability(multiple, stage) {
            // Updated graduation rates based on 2022-2024 market data
            // Much more conservative to reflect current tough funding environment
            
            // Base rates by stage (reflecting brutal 2022-2024 market reality)
            const baseRates = {
                'Seed': 0.15,      // 15% - bottom of the Carta/Crunchbase range
                'SeriesA': 0.45,   // 45% - Series A crunch continues 
                'SeriesB': 0.50,   // 50% - B to C is very challenging
                'SeriesC': 0.55,   // 55% - even proven companies struggle
                'Growth': 0.60     // 60% - exit environment still tough
            };
            
            const baseRate = baseRates[stage] || 0.45;
            
            // Very conservative performance multipliers - tough market punishes even good performance
            let performanceMultiplier = 1.0;
            
            if (multiple === 0) {
                performanceMultiplier = 0.02;  // Dead companies essentially never raise
            } else if (multiple < 0.5) {
                performanceMultiplier = 0.15;  // Severe underperformers have minimal chance
            } else if (multiple < 1.0) {
                performanceMultiplier = 0.4;   // Below water companies face major headwinds
            } else if (multiple < 1.5) {
                performanceMultiplier = 0.7;   // Modest performance insufficient in this market
            } else if (multiple < 2.0) {
                performanceMultiplier = 0.9;   // Good performance gets penalized vs base
            } else if (multiple < 3.0) {
                performanceMultiplier = 1.1;   // Strong performance gets slight boost
            } else if (multiple < 5.0) {
                // Exceptional performers still face challenges
                if (stage === 'Seed' || stage === 'SeriesA') {
                    performanceMultiplier = 1.3;  // Early stage exceptional get modest boost
                } else {
                    performanceMultiplier = 1.2;  // Later stage faces valuation/exit concerns
                }
            } else {
                // Truly exceptional (5x+) performers
                if (stage === 'Seed' || stage === 'SeriesA') {
                    performanceMultiplier = 1.5;  // Early stage outliers get better treatment
                } else {
                    performanceMultiplier = 1.3;  // Later stage still capped
                }
            }
            
            // Calculate final probability
            let probability = baseRate * performanceMultiplier;
            
            // Very strict stage caps reflecting brutal current market
            if (stage === 'Seed') {
                probability = Math.min(0.30, probability);  // Series A crunch is real
            } else if (stage === 'SeriesA') {
                probability = Math.min(0.65, probability);  // A to B remains very challenging
            } else if (stage === 'SeriesB') {
                probability = Math.min(0.65, probability);  // B to C is brutal
            } else if (stage === 'SeriesC') {
                probability = Math.min(0.70, probability);  // Even proven companies struggle
            } else if (stage === 'Growth') {
                probability = Math.min(0.75, probability);  // Exit environment constrains late stage
            } else {
                probability = Math.min(0.65, probability);  // General very conservative cap
            }
            
            return Math.max(0.02, probability);  // Minimum 2% chance (rescue rounds)
        }

        function runSingleStageOptimization() {
            const fundSize = parseFloat(document.getElementById('fundSize').value) * 1_000_000;
            const numTrials = parseInt(document.getElementById('numTrials').value);
            const targetNetTVPI = parseFloat(document.getElementById('targetNetTVPI').value);

            document.getElementById('results').classList.add('show');
            document.getElementById('resultsGrid').innerHTML = '<p style="text-align: center; color: #7f8c8d;">Running constrained optimization across mathematically feasible scenarios...</p>';

            const allResults = [];
            let bestResult = null;
            let bestScore = -1;

            // SIMPLIFIED: Test all portfolio sizes with standard follow-on percentages
            const portfolioSizes = [5, 7, 9, 11, 13, 15, 17, 19, 21, 22];
            const followOnReservePercents = [30, 40, 50];
            
            for (const portfolioSize of portfolioSizes) {
                for (const followOnReservePercent of followOnReservePercents) {
                    // Test different follow-on strategies
                    for (const strategyKey of Object.keys(FOLLOW_ON_STRATEGIES)) {
                        const strategy = FOLLOW_ON_STRATEGIES[strategyKey];
                        
                        const result = runConstrainedScenario(
                            fundSize, 
                            portfolioSize, 
                            followOnReservePercent, 
                            strategy, 
                            numTrials
                        );
                        
                        if (result.feasible) {
                            // Score combines TVPI achievement and capital efficiency
                            const score = result.netTVPI + (1 - result.dryPowderPercent / 100);
                            
                            allResults.push({
                                ...result,
                                portfolioSize,
                                followOnReservePercent,
                                strategy: strategy.name,
                                score
                            });

                            if (score > bestScore) {
                                bestScore = score;
                                bestResult = allResults[allResults.length - 1];
                            }
                        }
                    }
                }
            }

            // Option 2: Fixed follow-on reserves with calculated max portfolio sizes
            // (Reusing followOnReservePercents from above)
            
            for (const followOnReservePercent of followOnReservePercents) {
                const initialCapitalBudget = fundSize * (100 - followOnReservePercent) / 100;
                const avgCheckSize = STAGE_PARAMS[selectedStage].checkSizes.avg;
                const maxPortfolioSize = Math.floor(initialCapitalBudget / avgCheckSize);
                
                // Test reasonable portfolio sizes within this constraint
                const testSizes = [
                    Math.max(5, Math.floor(maxPortfolioSize * 0.6)),
                    Math.max(5, Math.floor(maxPortfolioSize * 0.8)),
                    Math.max(5, maxPortfolioSize)
                ].filter((size, index, arr) => arr.indexOf(size) === index); // Remove duplicates
                
                for (const portfolioSize of testSizes) {
                    for (const strategyKey of Object.keys(FOLLOW_ON_STRATEGIES)) {
                        const strategy = FOLLOW_ON_STRATEGIES[strategyKey];
                        
                        const result = runConstrainedScenario(
                            fundSize, 
                            portfolioSize, 
                            followOnReservePercent, 
                            strategy, 
                            numTrials
                        );
                        
                        if (result.feasible) {
                            const score = result.netTVPI + (1 - result.dryPowderPercent / 100);
                            
                            // Avoid duplicates
                            const isDuplicate = allResults.some(r => 
                                r.portfolioSize === portfolioSize && 
                                Math.abs(r.followOnReservePercent - followOnReservePercent) < 1 &&
                                r.strategy === strategy.name
                            );
                            
                            if (!isDuplicate) {
                                allResults.push({
                                    ...result,
                                    portfolioSize,
                                    followOnReservePercent,
                                    strategy: strategy.name,
                                    score
                                });

                                if (score > bestScore) {
                                    bestScore = score;
                                    bestResult = allResults[allResults.length - 1];
                                }
                            }
                        }
                    }
                }
            }

            console.log(`Generated ${allResults.length} mathematically feasible scenarios`);
            
            // Debug logging
            console.log('Sample results:', allResults.slice(0, 5));
            console.log('Portfolio sizes represented:', [...new Set(allResults.map(r => r.portfolioSize))].sort((a,b) => a-b));
            console.log('Strategies represented:', [...new Set(allResults.map(r => r.strategy))]);
            
            displayOptimizationResults(allResults, bestResult, targetNetTVPI);
            createOptimizationChart(allResults);
            generateOptimizationInsights(allResults, bestResult, targetNetTVPI);
        }

        function runConstrainedScenario(fundSize, portfolioSize, followOnReservePercent, strategy, numTrials) {
            const followOnReserve = fundSize * followOnReservePercent / 100;
            const initialCapital = fundSize - followOnReserve;
            
            // MATHEMATICAL CONSTRAINT CHECK
            const avgCheckSize = STAGE_PARAMS[selectedStage].checkSizes.avg;
            const estimatedInitialNeed = portfolioSize * avgCheckSize;
            
            // Check if scenario is mathematically feasible (more generous constraint)
            if (estimatedInitialNeed > initialCapital * 1.3) { // Allow 30% variance for randomness
                console.log(`Infeasible: ${portfolioSize} companies need ~$${(estimatedInitialNeed/1e6).toFixed(1)}M but only $${(initialCapital/1e6).toFixed(1)}M available`);
                return { feasible: false };
            }

            let totalGrossReturn = 0;
            let totalDeployed = 0;
            let totalFollowOnUsed = 0;
            let totalGraduationRate = 0;
            let actualCompaniesCreated = 0;
            
            for (let trial = 0; trial < numTrials; trial++) {
                const companies = [];
                let trialInitialCapital = 0;
                let trialFollowOnCapital = 0;
                let trialValue = 0;
                let trialGraduationCount = 0;

                // Create portfolio with capital constraint enforcement
                for (let i = 0; i < portfolioSize; i++) {
                    const checkSize = getRandomCheckSize(selectedStage);
                    if (trialInitialCapital + checkSize <= initialCapital) {
                        const multiple = drawMultiple(selectedStage);
                        const willGraduate = Math.random() < getGraduationProbability(multiple, selectedStage);
                        
                        companies.push({
                            id: i,
                            initialAmount: checkSize,
                            multiple: multiple,
                            willGraduate: willGraduate
                        });
                        
                        trialInitialCapital += checkSize;
                        trialValue += checkSize * multiple;
                        
                        if (willGraduate) trialGraduationCount++;
                    } else {
                        // Can't afford this company - stop creating companies
                        break;
                    }
                }
                
                // Calculate trial graduation rate
                if (companies.length > 0) {
                    totalGraduationRate += (trialGraduationCount / companies.length) * 100;
                }
                
                actualCompaniesCreated += companies.length;

                // Follow-on investments
                const followOnAmounts = strategy.getFollowOnAmount(companies, followOnReserve);
                
                for (let i = 0; i < companies.length; i++) {
                    const followOnAmount = followOnAmounts[i];
                    if (followOnAmount > 0) {
                        trialFollowOnCapital += followOnAmount;
                        
                        // Follow-on returns (next stage performance)
                        const nextStageMultiple = drawMultiple(STAGE_PARAMS[selectedStage].nextStage);
                        trialValue += followOnAmount * nextStageMultiple;
                    }
                }

                totalGrossReturn += trialValue;
                totalDeployed += trialInitialCapital + trialFollowOnCapital;
                totalFollowOnUsed += trialFollowOnCapital;
            }

            // Calculate metrics
            const avgGrossReturn = totalGrossReturn / numTrials;
            const avgDeployed = totalDeployed / numTrials;
            const avgFollowOnUsed = totalFollowOnUsed / numTrials;
            const grossTVPI = avgDeployed > 0 ? avgGrossReturn / avgDeployed : 0;
            
            // Net calculations
            const managementFees = fundSize * 0.02 * 10; // 2% for 10 years
            const profits = Math.max(0, avgGrossReturn - fundSize);
            const carriedInterest = profits * 0.20;
            const netReturn = avgGrossReturn - managementFees - carriedInterest;
            const netTVPI = netReturn / fundSize;
            
            const dryPowder = fundSize - avgDeployed;
            const dryPowderPercent = (dryPowder / fundSize) * 100;
            const followOnUtilization = followOnReserve > 0 ? (avgFollowOnUsed / followOnReserve) * 100 : 0;
            const actualAvgPortfolioSize = actualCompaniesCreated / numTrials;
            const graduationRate = totalGraduationRate / numTrials;

            return {
                feasible: true,
                grossTVPI,
                netTVPI,
                dryPowderPercent,
                followOnUtilization,
                graduationRate,
                avgDeployed: avgDeployed / 1e6,
                avgFollowOnUsed: avgFollowOnUsed / 1e6,
                actualAvgPortfolioSize,
                estimatedInitialNeed: estimatedInitialNeed / 1e6,
                initialCapitalAvailable: initialCapital / 1e6
            };
        }

        function displayOptimizationResults(allResults, bestResult, targetNetTVPI) {
            const grid = document.getElementById('resultsGrid');
            
            // Show optimal result prominently
            grid.innerHTML = `
                <div class="metric-card" style="grid-column: 1 / -1; background: #e8f0fe; border: 2px solid #1a73e8;">
                    <h3 style="margin-bottom: 16px; color: #1a73e8; font-weight: 500;">🏆 OPTIMAL CONFIGURATION</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 12px;">
                        <div style="text-align: center;">
                            <div style="font-size: 1.5em; font-weight: 500; color: #1a73e8;">${bestResult.portfolioSize}</div>
                            <div style="color: #5f6368; font-size: 0.875em;">Companies</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.5em; font-weight: 500; color: #1a73e8;">${bestResult.followOnReservePercent}%</div>
                            <div style="color: #5f6368; font-size: 0.875em;">Follow-On Reserve</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.5em; font-weight: 500; color: #1a73e8;">${bestResult.strategy}</div>
                            <div style="color: #5f6368; font-size: 0.875em;">Strategy</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.5em; font-weight: 500; color: ${bestResult.netTVPI >= targetNetTVPI ? '#188038' : '#c5221f'};">${bestResult.netTVPI.toFixed(2)}x</div>
                            <div style="color: #5f6368; font-size: 0.875em;">Net TVPI</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.5em; font-weight: 500; color: #1a73e8;">${bestResult.dryPowderPercent.toFixed(1)}%</div>
                            <div style="color: #5f6368; font-size: 0.875em;">Dry Powder</div>
                        </div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value">${bestResult.grossTVPI.toFixed(2)}x</div>
                    <div class="metric-label">Gross TVPI</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${bestResult.followOnUtilization.toFixed(1)}%</div>
                    <div class="metric-label">Follow-On Utilization</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${bestResult.graduationRate.toFixed(1)}%</div>
                    <div class="metric-label">Avg Follow-On Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$${bestResult.avgDeployed.toFixed(1)}M</div>
                    <div class="metric-label">Capital Deployed</div>
                </div>
            `;

            // Add detailed comparison table
            const comparisonDiv = document.createElement('div');
            comparisonDiv.innerHTML = `
                <div style="margin-top: 30px;">
                    <h3 style="margin-bottom: 15px;">📊 Top 10 Configurations</h3>
                    <div style="overflow-x: auto; -webkit-overflow-scrolling: touch; margin: 0 -20px; padding: 0 20px;">
                        <table style="min-width: 600px; width: 100%; border-collapse: collapse; font-size: 0.85em;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 8px 6px; border: 1px solid #dee2e6; white-space: nowrap;">Rank</th>
                                    <th style="padding: 8px 6px; border: 1px solid #dee2e6; white-space: nowrap;">Companies</th>
                                    <th style="padding: 8px 6px; border: 1px solid #dee2e6; white-space: nowrap;">Reserve %</th>
                                    <th style="padding: 8px 6px; border: 1px solid #dee2e6; min-width: 120px;">Strategy</th>
                                    <th style="padding: 8px 6px; border: 1px solid #dee2e6; white-space: nowrap;">Net TVPI</th>
                                    <th style="padding: 8px 6px; border: 1px solid #dee2e6; white-space: nowrap;">Dry Powder</th>
                                    <th style="padding: 8px 6px; border: 1px solid #dee2e6; white-space: nowrap;">Score</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${allResults
                                    .sort((a, b) => b.score - a.score)
                                    .slice(0, 10)
                                    .map((result, index) => `
                                        <tr style="${index === 0 ? 'background: #e8f5e8; font-weight: 600;' : ''}">
                                            <td style="padding: 8px 6px; border: 1px solid #dee2e6; text-align: center;">${index + 1}</td>
                                            <td style="padding: 8px 6px; border: 1px solid #dee2e6; text-align: center;">${result.portfolioSize}</td>
                                            <td style="padding: 8px 6px; border: 1px solid #dee2e6; text-align: center;">${result.followOnReservePercent}%</td>
                                            <td style="padding: 8px 6px; border: 1px solid #dee2e6; font-size: 0.85em;">${result.strategy}</td>
                                            <td style="padding: 8px 6px; border: 1px solid #dee2e6; text-align: center; color: ${result.netTVPI >= targetNetTVPI ? '#27ae60' : '#e74c3c'};">${result.netTVPI.toFixed(2)}x</td>
                                            <td style="padding: 8px 6px; border: 1px solid #dee2e6; text-align: center;">${result.dryPowderPercent.toFixed(1)}%</td>
                                            <td style="padding: 8px 6px; border: 1px solid #dee2e6; text-align: center;">${result.score.toFixed(2)}</td>
                                        </tr>
                                    `).join('')}
                            </tbody>
                        </table>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.85em; color: #7f8c8d; text-align: center;">
                        <em>Scroll horizontally to see all columns on mobile devices</em>
                    </div>
                </div>
                
                <!-- Mobile card view for very small screens -->
                <div class="mobile-results-view" style="display: none;">
                    <h3 style="margin-bottom: 15px;">📊 Top 10 Configurations</h3>
                    ${allResults
                        .sort((a, b) => b.score - a.score)
                        .slice(0, 10)
                        .map((result, index) => `
                            <div style="background: ${index === 0 ? '#e8f5e8' : '#f8f9fa'}; border: 1px solid ${index === 0 ? '#c8e6c9' : '#dee2e6'}; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                    <span style="font-weight: 700; color: ${index === 0 ? '#2e7d32' : '#2c3e50'};">Rank #${index + 1}</span>
                                    <span style="font-weight: 600; color: ${result.netTVPI >= targetNetTVPI ? '#27ae60' : '#e74c3c'};">${result.netTVPI.toFixed(2)}x Net TVPI</span>
                                </div>
                                <div style="font-size: 0.9em; color: #666;">
                                    <div style="margin-bottom: 5px;"><strong>${result.portfolioSize}</strong> companies • <strong>${result.followOnReservePercent}%</strong> reserve</div>
                                    <div style="margin-bottom: 5px;">Strategy: <strong>${result.strategy}</strong></div>
                                    <div>Dry Powder: <strong>${result.dryPowderPercent.toFixed(1)}%</strong> • Score: <strong>${result.score.toFixed(2)}</strong></div>
                                </div>
                            </div>
                        `).join('')}
                </div>
                
                <style>
                    @media (max-width: 600px) {
                        .mobile-results-view {
                            display: block !important;
                        }
                        div[style*="overflow-x: auto"] {
                            display: none;
                        }
                    }
                </style>
            `;
            
            grid.appendChild(comparisonDiv);
        }

        function createOptimizationChart(allResults) {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            if (performanceChart) {
                performanceChart.destroy();
            }

            if (allResults.length === 0) {
                console.log('No results to chart');
                return;
            }

            // Create line chart: Portfolio Size vs Net TVPI, with curves for each strategy
            const datasets = [];
            const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12'];
            const strategies = ['Equal Allocation', 'Performance-Based', 'Aggressive Winners', 'Highly Selective'];

            console.log('Creating chart with', allResults.length, 'results');

            strategies.forEach((strategy, index) => {
                // Get all results for this strategy, sorted by portfolio size
                const strategyData = allResults
                    .filter(r => r.strategy === strategy)
                    .filter(r => r.netTVPI && !isNaN(r.netTVPI)) // Filter out invalid data
                    .sort((a, b) => a.portfolioSize - b.portfolioSize)
                    .map(r => ({
                        x: r.portfolioSize,
                        y: r.netTVPI,
                        followOnReserve: r.followOnReservePercent || 0,
                        dryPowder: r.dryPowderPercent || 0
                    }));

                console.log(`${strategy}: ${strategyData.length} data points`);

                if (strategyData.length > 0) {
                    // Group by portfolio size and average the TVPI (in case of multiple scenarios)
                    const groupedData = {};
                    strategyData.forEach(point => {
                        if (!groupedData[point.x]) {
                            groupedData[point.x] = [];
                        }
                        groupedData[point.x].push(point);
                    });

                    const avgData = Object.entries(groupedData).map(([portfolioSize, points]) => {
                        const avgTVPI = points.reduce((sum, p) => sum + p.y, 0) / points.length;
                        const avgFollowOn = points.reduce((sum, p) => sum + p.followOnReserve, 0) / points.length;
                        const avgDryPowder = points.reduce((sum, p) => sum + p.dryPowder, 0) / points.length;
                        return {
                            x: parseInt(portfolioSize),
                            y: avgTVPI,
                            followOnReserve: avgFollowOn,
                            dryPowder: avgDryPowder
                        };
                    }).sort((a, b) => a.x - b.x);

                    console.log(`${strategy} averaged data:`, avgData);
                    console.log(`X values for ${strategy}:`, avgData.map(d => d.x));

                    if (avgData.length > 0) {
                        datasets.push({
                            label: strategy,
                            data: avgData,
                            borderColor: colors[index],
                            backgroundColor: colors[index] + '20', // 20% opacity
                            pointBackgroundColor: colors[index],
                            pointBorderColor: colors[index],
                            pointRadius: 6,
                            pointHoverRadius: 8,
                            fill: false,
                            tension: 0.2, // Less aggressive smoothing
                            borderWidth: 3,
                            spanGaps: true // Connect points even if some are missing
                        });
                    }
                }
            });

            console.log('Final datasets:', datasets.length);

            // Determine axis ranges from actual data
            const allXValues = allResults.map(r => r.portfolioSize).filter(x => !isNaN(x));
            const allYValues = allResults.map(r => r.netTVPI).filter(y => !isNaN(y) && y > 0);
            
            const minX = Math.min(...allXValues);
            const maxX = Math.max(...allXValues);
            const maxY = Math.max(...allYValues);

            performanceChart = new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${selectedStage} Strategy Performance: Portfolio Size vs Net TVPI`
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const point = context.parsed;
                                    const dataPoint = context.raw;
                                    return [
                                        `${context.dataset.label}`,
                                        `Companies: ${point.x}`,
                                        `Net TVPI: ${point.y.toFixed(2)}x`,
                                        `Avg Follow-On: ${dataPoint.followOnReserve.toFixed(0)}%`,
                                        `Avg Dry Powder: ${dataPoint.dryPowder.toFixed(1)}%`
                                    ];
                                }
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            title: {
                                display: true,
                                text: 'Number of Portfolio Companies'
                            },
                            min: 4,
                            max: 24,
                            ticks: {
                                stepSize: 2,
                                callback: function(value) {
                                    return Math.round(value);
                                }
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Net TVPI Multiple'
                            },
                            min: 0,
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        function generateOptimizationInsights(allResults, bestResult, targetNetTVPI) {
            const insights = document.getElementById('insights');
            
            let content = '<h3>🔍 Optimization Insights</h3>';
            
            // Optimal configuration analysis
            content += `<div class="insight-item">🏆 <strong>Optimal Configuration:</strong> ${bestResult.portfolioSize} companies with ${bestResult.followOnReservePercent.toFixed(0)}% follow-on reserve using ${bestResult.strategy} strategy achieves ${bestResult.netTVPI.toFixed(2)}x net TVPI</div>`;
            
            // Mathematical feasibility analysis
            const totalScenarios = 6 * 5 * 4; // portfolioSizes * followOnPercents * strategies
            const feasibleScenarios = allResults.length;
            const feasibilityRate = (feasibleScenarios / totalScenarios) * 100;
            
            content += `<div class="insight-item">⚖️ <strong>Fund Constraints:</strong> Only ${feasibilityRate.toFixed(0)}% of theoretical combinations (${feasibleScenarios}/${totalScenarios}) are mathematically feasible due to fund size limits</div>`;
            
            // Portfolio size sweet spot analysis
            const portfolioSizeGroups = {};
            allResults.forEach(r => {
                const bucket = Math.floor(r.portfolioSize / 5) * 5; // Group by 5s
                if (!portfolioSizeGroups[bucket]) portfolioSizeGroups[bucket] = [];
                portfolioSizeGroups[bucket].push(r.netTVPI);
            });
            
            let bestPortfolioRange = null;
            let bestAvgTVPI = 0;
            Object.entries(portfolioSizeGroups).forEach(([range, tvpis]) => {
                const avgTVPI = tvpis.reduce((sum, tvpi) => sum + tvpi, 0) / tvpis.length;
                if (avgTVPI > bestAvgTVPI) {
                    bestAvgTVPI = avgTVPI;
                    bestPortfolioRange = range;
                }
            });
            
            if (bestPortfolioRange) {
                content += `<div class="insight-item">📊 <strong>Portfolio Size Sweet Spot:</strong> ${bestPortfolioRange}-${parseInt(bestPortfolioRange) + 4} companies consistently deliver highest returns (${bestAvgTVPI.toFixed(2)}x average)</div>`;
            }
            
            // Follow-on strategy effectiveness
            const strategyGroups = {};
            allResults.forEach(r => {
                if (!strategyGroups[r.strategy]) strategyGroups[r.strategy] = [];
                strategyGroups[r.strategy].push(r.netTVPI);
            });
            
            let bestStrategy = null;
            let bestStrategyTVPI = 0;
            Object.entries(strategyGroups).forEach(([strategy, tvpis]) => {
                const avgTVPI = tvpis.reduce((sum, tvpi) => sum + tvpi, 0) / tvpis.length;
                if (avgTVPI > bestStrategyTVPI) {
                    bestStrategyTVPI = avgTVPI;
                    bestStrategy = strategy;
                }
            });
            
            content += `<div class="insight-item">🎯 <strong>Best Follow-On Strategy:</strong> ${bestStrategy} delivers highest average returns (${bestStrategyTVPI.toFixed(2)}x) across all portfolio configurations</div>`;
            
            // Capital efficiency insights
            const avgDryPowder = allResults.reduce((sum, r) => sum + r.dryPowderPercent, 0) / allResults.length;
            if (avgDryPowder < 10) {
                content += `<div class="insight-item">⚡ <strong>Capital Efficiency:</strong> Low average dry powder (${avgDryPowder.toFixed(1)}%) indicates ${selectedStage} stage allows efficient capital deployment</div>`;
            } else {
                content += `<div class="insight-item">💰 <strong>Capital Slack:</strong> High average dry powder (${avgDryPowder.toFixed(1)}%) suggests either over-reserving or insufficient graduation rates in ${selectedStage}</div>`;
            }
            
            // Target achievement analysis
            const targetsAchieved = allResults.filter(r => r.netTVPI >= targetNetTVPI).length;
            const achievementRate = (targetsAchieved / allResults.length) * 100;
            
            if (achievementRate > 50) {
                content += `<div class="insight-item">✅ <strong>Target Feasibility:</strong> ${achievementRate.toFixed(0)}% of configurations meet ${targetNetTVPI}x target - realistic goal for ${selectedStage} investing</div>`;
            } else {
                content += `<div class="insight-item">🎯 <strong>Target Challenge:</strong> Only ${achievementRate.toFixed(0)}% of configurations meet ${targetNetTVPI}x target - consider adjusting expectations for ${selectedStage} stage</div>`;
            }
            
            // Stage-specific insights
            const stageInsights = {
                'Seed': 'Early-stage optimization shows high sensitivity to follow-on strategy - winners need aggressive support',
                'SeriesA': 'Series A optimization balances portfolio size with follow-on firepower for scaling companies',
                'SeriesB': 'Series B sweet spot trades diversification against meaningful ownership in proven models',
                'SeriesC': 'Series C optimization shows diminishing returns from large follow-on reserves due to limited headroom',
                'Growth': 'Growth stage optimization favors larger portfolios with minimal follow-on allocation'
            };
            
            content += `<div class="insight-item">📈 <strong>${selectedStage} Characteristics:</strong> ${stageInsights[selectedStage]}</div>`;

            insights.innerHTML = content;
        }
    </script>
</body>
</html> 