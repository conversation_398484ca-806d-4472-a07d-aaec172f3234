<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Tracker & Follow-On Advisor</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 40px;
            font-size: 1.1em;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }

        .fund-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .metric-value {
            font-size: 2em;
            font-weight: 700;
            color: #3498db;
            margin: 10px 0;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        input, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #3498db;
        }

        button {
            padding: 14px 24px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .add-investment-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            align-items: end;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .portfolio-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .portfolio-table th,
        .portfolio-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .portfolio-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .portfolio-table tr:hover {
            background-color: #f1f3f4;
        }

        .stage-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
            color: white;
        }

        .stage-seed { background-color: #e74c3c; }
        .stage-seriesa { background-color: #f39c12; }
        .stage-seriesb { background-color: #f1c40f; color: #333; }
        .stage-seriesc { background-color: #2ecc71; }
        .stage-growth { background-color: #9b59b6; }

        .tvpi-good { color: #27ae60; font-weight: 600; }
        .tvpi-ok { color: #f39c12; font-weight: 600; }
        .tvpi-poor { color: #e74c3c; font-weight: 600; }

        .recommendation {
            padding: 10px;
            border-radius: 6px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .rec-followon { background-color: #d5f4e6; color: #27ae60; }
        .rec-hold { background-color: #fef9e7; color: #f39c12; }
        .rec-stop { background-color: #fadbd8; color: #e74c3c; }

        .delete-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .delete-btn:hover {
            background: #c0392b;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .chart-container {
            height: 300px;
            margin-top: 20px;
        }

        .recommendations-section {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }

        @media (max-width: 768px) {
            .add-investment-form {
                grid-template-columns: 1fr;
            }
            
            .fund-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Portfolio Tracker</h1>
        <p class="subtitle">Track your active investments and get AI-powered follow-on recommendations</p>

        <!-- Fund Overview -->
        <div class="card">
            <h2>Fund Overview</h2>
            <div class="input-group" style="max-width: 300px;">
                <label for="fundSize">Fund Size (millions)</label>
                <input type="number" id="fundSize" value="400" min="50" max="5000" step="50" onchange="updateFundMetrics()">
            </div>
            
            <div class="fund-overview">
                <div class="metric-card">
                    <div class="metric-label">Fund Size</div>
                    <div class="metric-value" id="totalFundSize">$400M</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Total Invested</div>
                    <div class="metric-value" id="totalInvested">$0M</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Remaining Capital</div>
                    <div class="metric-value" id="remainingCapital">$400M</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Portfolio TVPI</div>
                    <div class="metric-value" id="portfolioTVPI">0.00x</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Active Companies</div>
                    <div class="metric-value" id="activeCompanies">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Deployment %</div>
                    <div class="metric-value" id="deploymentPercent">0%</div>
                </div>
            </div>
        </div>

        <!-- Add Investment -->
        <div class="card">
            <h2>Add Investment</h2>
            <div class="add-investment-form">
                <div class="input-group">
                    <label for="companyName">Company Name</label>
                    <input type="text" id="companyName" placeholder="e.g., AcmeCorp">
                </div>
                <div class="input-group">
                    <label for="initialStage">Initial Stage</label>
                    <select id="initialStage">
                        <option value="Seed">Seed</option>
                        <option value="Series A">Series A</option>
                        <option value="Series B">Series B</option>
                        <option value="Series C">Series C</option>
                        <option value="Growth">Growth</option>
                    </select>
                </div>
                <div class="input-group">
                    <label for="initialAmount">Initial Investment ($M)</label>
                    <input type="number" id="initialAmount" placeholder="5.0" step="0.1" min="0.1">
                </div>
                <div class="input-group">
                    <label for="currentTVPI">Current TVPI</label>
                    <input type="number" id="currentTVPI" placeholder="1.0" step="0.1" min="0">
                </div>
                <div class="input-group">
                    <label for="followOnCount">Follow-On Rounds</label>
                    <input type="number" id="followOnCount" value="0" min="0" max="5" onchange="updateFollowOnInputs()">
                </div>
                <div class="input-group">
                    <button onclick="addInvestment()">Add Investment</button>
                </div>
            </div>
            
            <!-- Dynamic Follow-On Inputs -->
            <div id="followOnInputs" style="display: none; background: #f0f8ff; padding: 20px; border-radius: 8px; margin-top: 20px;">
                <h3>Follow-On Investment Details</h3>
                <div id="followOnRounds"></div>
            </div>
        </div>

        <!-- Portfolio Table -->
        <div class="card">
            <h2>Active Portfolio</h2>
            <table class="portfolio-table">
                <thead>
                    <tr>
                        <th>Company</th>
                        <th>Initial Stage</th>
                        <th>Initial Investment</th>
                        <th>Follow-On Rounds</th>
                        <th>Total Invested</th>
                        <th>Current TVPI</th>
                        <th>Recommendation</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="portfolioTableBody">
                    <!-- Portfolio entries will be added here -->
                </tbody>
            </table>
        </div>

        <!-- AI Recommendations -->
        <div class="card">
            <h2>AI Portfolio Recommendations</h2>
            <div class="recommendations-section" id="aiRecommendations">
                <p>Add some investments to get personalized recommendations!</p>
            </div>
        </div>

        <!-- Charts and Analysis -->
        <div class="card">
            <h2>Portfolio Analysis</h2>
            <div class="summary-cards">
                <div class="metric-card">
                    <div class="metric-label">Avg TVPI</div>
                    <div class="metric-value" id="avgTVPI">0.00x</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Follow-On Candidates</div>
                    <div class="metric-value" id="followOnCandidates">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Portfolio at Risk</div>
                    <div class="metric-value" id="portfolioAtRisk">$0M</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Unrealized Value</div>
                    <div class="metric-value" id="unrealizedValue">$0M</div>
                </div>
            </div>
            
            <div class="chart-container">
                <canvas id="portfolioChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        let portfolio = [];
        let portfolioChart = null;

        function updateFollowOnInputs() {
            const followOnCount = parseInt(document.getElementById('followOnCount').value) || 0;
            const followOnInputs = document.getElementById('followOnInputs');
            const followOnRounds = document.getElementById('followOnRounds');
            
            if (followOnCount === 0) {
                followOnInputs.style.display = 'none';
                return;
            }
            
            followOnInputs.style.display = 'block';
            followOnRounds.innerHTML = '';
            
            for (let i = 0; i < followOnCount; i++) {
                const roundDiv = document.createElement('div');
                roundDiv.style.cssText = 'display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px; padding: 15px; background: white; border-radius: 6px;';
                roundDiv.innerHTML = `
                    <div>
                        <label>Follow-On ${i + 1} Stage</label>
                        <select id="followOnStage${i}">
                            <option value="Seed">Seed</option>
                            <option value="Series A">Series A</option>
                            <option value="Series B">Series B</option>
                            <option value="Series C">Series C</option>
                            <option value="Growth">Growth</option>
                        </select>
                    </div>
                    <div>
                        <label>Amount ($M)</label>
                        <input type="number" id="followOnAmount${i}" placeholder="2.0" step="0.1" min="0.1">
                    </div>
                    <div>
                        <label>Months Ago</label>
                        <input type="number" id="followOnMonths${i}" placeholder="6" min="0" max="60">
                    </div>
                `;
                followOnRounds.appendChild(roundDiv);
            }
        }

        function updateFundMetrics() {
            const fundSize = parseFloat(document.getElementById('fundSize').value) || 400;
            const totalInvested = portfolio.reduce((sum, inv) => sum + inv.totalAmount, 0);
            const remainingCapital = fundSize - totalInvested;
            const totalValue = portfolio.reduce((sum, inv) => sum + (inv.totalAmount * inv.tvpi), 0);
            const portfolioTVPI = totalInvested > 0 ? totalValue / totalInvested : 0;
            const deploymentPercent = (totalInvested / fundSize) * 100;

            document.getElementById('totalFundSize').textContent = `$${fundSize}M`;
            document.getElementById('totalInvested').textContent = `$${totalInvested.toFixed(1)}M`;
            document.getElementById('remainingCapital').textContent = `$${remainingCapital.toFixed(1)}M`;
            document.getElementById('portfolioTVPI').textContent = `${portfolioTVPI.toFixed(2)}x`;
            document.getElementById('activeCompanies').textContent = portfolio.length;
            document.getElementById('deploymentPercent').textContent = `${deploymentPercent.toFixed(1)}%`;

            updateAnalytics();
        }

        function addInvestment() {
            const name = document.getElementById('companyName').value.trim();
            const initialStage = document.getElementById('initialStage').value;
            const initialAmount = parseFloat(document.getElementById('initialAmount').value);
            const tvpi = parseFloat(document.getElementById('currentTVPI').value);
            const followOnCount = parseInt(document.getElementById('followOnCount').value) || 0;

            if (!name || !initialAmount || initialAmount <= 0 || !tvpi || tvpi < 0) {
                alert('Please fill in all fields with valid values');
                return;
            }

            // Collect follow-on data
            const followOns = [];
            let totalFollowOnAmount = 0;
            
            for (let i = 0; i < followOnCount; i++) {
                const stage = document.getElementById(`followOnStage${i}`).value;
                const amount = parseFloat(document.getElementById(`followOnAmount${i}`).value);
                const monthsAgo = parseInt(document.getElementById(`followOnMonths${i}`).value);
                
                if (!amount || amount <= 0) {
                    alert(`Please enter a valid amount for Follow-On ${i + 1}`);
                    return;
                }
                
                followOns.push({ stage, amount, monthsAgo });
                totalFollowOnAmount += amount;
            }

            const investment = {
                id: Date.now(),
                name,
                initialStage,
                initialAmount,
                followOns,
                followOnCount,
                totalAmount: initialAmount + totalFollowOnAmount,
                tvpi,
                dateAdded: new Date()
            };

            portfolio.push(investment);
            
            // Clear form
            document.getElementById('companyName').value = '';
            document.getElementById('initialAmount').value = '';
            document.getElementById('currentTVPI').value = '';
            document.getElementById('followOnCount').value = '0';
            updateFollowOnInputs();

            updatePortfolioTable();
            updateFundMetrics();
            generateRecommendations();
            updateChart();
        }

        function deleteInvestment(id) {
            if (confirm('Are you sure you want to delete this investment?')) {
                portfolio = portfolio.filter(inv => inv.id !== id);
                updatePortfolioTable();
                updateFundMetrics();
                generateRecommendations();
                updateChart();
            }
        }

        function updatePortfolioTable() {
            const tbody = document.getElementById('portfolioTableBody');
            tbody.innerHTML = '';

            portfolio.forEach(inv => {
                const row = document.createElement('tr');
                
                const stageBadgeClass = `stage-${inv.initialStage.toLowerCase().replace(' ', '')}`;
                const tvpiClass = inv.tvpi >= 2 ? 'tvpi-good' : inv.tvpi >= 1 ? 'tvpi-ok' : 'tvpi-poor';
                const recommendation = getRecommendation(inv);
                
                // Format follow-on summary
                let followOnSummary = inv.followOnCount === 0 ? 'None' : 
                    `${inv.followOnCount} rounds ($${(inv.totalAmount - inv.initialAmount).toFixed(1)}M)`;
                
                row.innerHTML = `
                    <td>${inv.name}</td>
                    <td><span class="stage-badge ${stageBadgeClass}">${inv.initialStage}</span></td>
                    <td>$${inv.initialAmount.toFixed(1)}M</td>
                    <td>${followOnSummary}</td>
                    <td><strong>$${inv.totalAmount.toFixed(1)}M</strong></td>
                    <td class="${tvpiClass}">${inv.tvpi.toFixed(2)}x</td>
                    <td><span class="recommendation ${recommendation.class}">${recommendation.text}</span></td>
                    <td><button class="delete-btn" onclick="deleteInvestment(${inv.id})">Delete</button></td>
                `;
                
                tbody.appendChild(row);
            });
        }

        function getRecommendation(investment) {
            const { initialStage, tvpi, followOnCount, totalAmount, initialAmount } = investment;
            
            // Advanced follow-on recommendation logic - aligned with simulator strategy
            const followOnRatio = followOnCount > 0 ? (totalAmount - initialAmount) / initialAmount : 0;
            
            if (tvpi >= 3 && followOnCount < 3) {
                return { text: '🚀 Strong Follow-On', class: 'rec-followon' };
            } else if (tvpi >= 2.5 && followOnCount === 0) {
                return { text: '💰 Immediate Follow-On', class: 'rec-followon' };
            } else if (tvpi >= 2.0 && followOnCount < 2) {
                return { text: '💡 Consider Follow-On', class: 'rec-followon' };
            } else if (tvpi >= 1.5 && tvpi < 2.0 && followOnCount === 0) {
                return { text: '🤔 Cautious Follow-On', class: 'rec-hold' };
            } else if (tvpi >= 1.0 && tvpi < 1.5) {
                return { text: '⏳ Hold & Monitor (Avoid Follow-On)', class: 'rec-hold' };
            } else if (tvpi >= 1.0 && followOnCount >= 3) {
                return { text: '⏸️ Follow-On Limit Reached', class: 'rec-hold' };
            } else if (tvpi < 1.0) {
                return { text: '⚠️ No Follow-On', class: 'rec-stop' };
            } else {
                return { text: '🔍 Needs Assessment', class: 'rec-hold' };
            }
        }

        function generateRecommendations() {
            if (portfolio.length === 0) {
                document.getElementById('aiRecommendations').innerHTML = 
                    '<p>Add some investments to get personalized recommendations!</p>';
                return;
            }

            const fundSize = parseFloat(document.getElementById('fundSize').value) || 400;
            const totalInvested = portfolio.reduce((sum, inv) => sum + inv.totalAmount, 0);
            const remainingCapital = fundSize - totalInvested;
            
            const highPerformers = portfolio.filter(inv => inv.tvpi >= 2).length;
            const underPerformers = portfolio.filter(inv => inv.tvpi < 1.0).length;
            const followOnCandidates = portfolio.filter(inv => inv.tvpi >= 2.0 && inv.followOnCount < 2).length;
            const mediocrePerformers = portfolio.filter(inv => inv.tvpi >= 1.0 && inv.tvpi < 2.0).length;
            const overInvested = portfolio.filter(inv => inv.followOnCount >= 3).length;
            
            // Calculate follow-on capital allocation
            const totalFollowOnCapital = portfolio.reduce((sum, inv) => sum + (inv.totalAmount - inv.initialAmount), 0);
            const followOnPercentOfFund = (totalFollowOnCapital / fundSize) * 100;
            
            let recommendations = '<h3>💡 Strategic Recommendations</h3>';
            
            if (followOnCandidates > 0) {
                recommendations += `<p><strong>Follow-On Priority:</strong> You have ${followOnCandidates} strong candidates (≥2.0x TVPI) for follow-on investment. Consider allocating $${(remainingCapital * 0.4).toFixed(1)}M for follow-ons.</p>`;
            }
            
            if (mediocrePerformers > 0) {
                const mediocreCapital = portfolio.filter(inv => inv.tvpi >= 1.0 && inv.tvpi < 2.0).reduce((sum, inv) => sum + inv.totalAmount, 0);
                recommendations += `<p><strong>Mediocre Performers:</strong> ${mediocrePerformers} companies (${mediocreCapital.toFixed(1)}M) are in the 1.0x-2.0x range. Generally avoid follow-ons unless exceptional circumstances.</p>`;
            }
            
            if (overInvested > 0) {
                recommendations += `<p><strong>Follow-On Discipline:</strong> ${overInvested} companies have received 3+ follow-on rounds. Focus new follow-on capital on fresh opportunities.</p>`;
            }
            
            if (underPerformers > 0) {
                const underPerformingCapital = portfolio.filter(inv => inv.tvpi < 1.0).reduce((sum, inv) => sum + inv.totalAmount, 0);
                recommendations += `<p><strong>Portfolio Risk:</strong> ${underPerformers} companies ($${underPerformingCapital.toFixed(1)}M) are underperforming (<1.0x). Avoid additional follow-on investments.</p>`;
            }
            
            recommendations += `<p><strong>Follow-On Analysis:</strong> You've deployed $${totalFollowOnCapital.toFixed(1)}M in follow-on capital (${followOnPercentOfFund.toFixed(1)}% of fund). Target range is 20-40% for optimal performance.</p>`;
            
            if (remainingCapital > fundSize * 0.3) {
                recommendations += `<p><strong>Deployment Strategy:</strong> $${remainingCapital.toFixed(1)}M remaining (${((remainingCapital/fundSize)*100).toFixed(1)}%). Balance between new investments and follow-on reserves.</p>`;
            }

            document.getElementById('aiRecommendations').innerHTML = recommendations;
        }

        function updateAnalytics() {
            if (portfolio.length === 0) {
                document.getElementById('avgTVPI').textContent = '0.00x';
                document.getElementById('followOnCandidates').textContent = '0';
                document.getElementById('portfolioAtRisk').textContent = '$0M';
                document.getElementById('unrealizedValue').textContent = '$0M';
                return;
            }

            const avgTVPI = portfolio.reduce((sum, inv) => sum + inv.tvpi, 0) / portfolio.length;
            const followOnCandidates = portfolio.filter(inv => inv.tvpi >= 2.0 && inv.followOnCount < 2).length;
            const portfolioAtRisk = portfolio.filter(inv => inv.tvpi < 1.0).reduce((sum, inv) => sum + inv.totalAmount, 0);
            const unrealizedValue = portfolio.reduce((sum, inv) => sum + (inv.totalAmount * inv.tvpi) - inv.totalAmount, 0);

            document.getElementById('avgTVPI').textContent = `${avgTVPI.toFixed(2)}x`;
            document.getElementById('followOnCandidates').textContent = followOnCandidates;
            document.getElementById('portfolioAtRisk').textContent = `$${portfolioAtRisk.toFixed(1)}M`;
            document.getElementById('unrealizedValue').textContent = `$${unrealizedValue.toFixed(1)}M`;
        }

        function updateChart() {
            const ctx = document.getElementById('portfolioChart').getContext('2d');
            
            if (portfolioChart) {
                portfolioChart.destroy();
            }
            
            if (portfolio.length === 0) return;

            // Group by initial stage
            const stageData = {};
            portfolio.forEach(inv => {
                if (!stageData[inv.initialStage]) {
                    stageData[inv.initialStage] = { count: 0, invested: 0, value: 0 };
                }
                stageData[inv.initialStage].count++;
                stageData[inv.initialStage].invested += inv.totalAmount;
                stageData[inv.initialStage].value += inv.totalAmount * inv.tvpi;
            });

            const stages = Object.keys(stageData);
            const investments = stages.map(stage => stageData[stage].invested);
            const values = stages.map(stage => stageData[stage].value);

            portfolioChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: stages,
                    datasets: [
                        {
                            label: 'Total Invested ($M)',
                            data: investments,
                            backgroundColor: 'rgba(52, 152, 219, 0.6)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 2
                        },
                        {
                            label: 'Current Value ($M)',
                            data: values,
                            backgroundColor: 'rgba(46, 204, 113, 0.6)',
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Total Investment vs Current Value by Initial Stage'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Amount ($M)'
                            }
                        }
                    }
                }
            });
        }

        // Initialize
        updateFundMetrics();
    </script>
</body>
</html> 