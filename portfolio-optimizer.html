<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VC Portfolio Optimizer</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 40px;
            font-size: 1.1em;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }

        .input-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        input[type="number"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="number"]:focus {
            outline: none;
            border-color: #3498db;
        }

        input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            cursor: pointer;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
        }

        button {
            flex: 1;
            padding: 14px 24px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }

        .secondary-button {
            background: #7f8c8d;
        }

        .secondary-button:hover {
            background: #5f6c7d;
        }

        .results {
            display: none;
        }

        .results.show {
            display: block;
        }

        .portfolio-mix {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .mix-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .mix-item:last-child {
            border-bottom: none;
        }

        .stage-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .metric-value {
            font-size: 2em;
            font-weight: 700;
            color: #3498db;
            margin: 10px 0;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }

        .progress-bar.show {
            display: block;
        }

        .progress-fill {
            height: 100%;
            background: #3498db;
            width: 0%;
            transition: width 0.3s;
        }

        .status-message {
            text-align: center;
            color: #7f8c8d;
            margin: 10px 0;
            font-style: italic;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.9em;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .two-column-grid {
                grid-template-columns: 1fr !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VC Portfolio Optimizer</h1>
        <p class="subtitle">Monte Carlo simulation for optimal venture capital portfolio allocation</p>
        <p class="subtitle" style="font-size: 0.9em; margin-top: -30px; color: #95a5a6;">
            Now with dynamic check sizes: each investment draws from realistic stage-specific ranges
        </p>

        <div class="card">
            <h2>Configuration</h2>
            <div class="input-group">
                <label for="fundSize">
                    Fund Size (millions)
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Total fund size in millions of dollars</span>
                    </span>
                </label>
                <input type="number" id="fundSize" value="300" min="10" max="10000" step="10">
            </div>
            <div class="input-group">
                <label for="numTrials">
                    Monte Carlo Trials
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Number of simulations per portfolio mix (higher = more accurate but slower)</span>
                    </span>
                </label>
                <input type="number" id="numTrials" value="10000" min="1000" max="100000" step="1000">
            </div>
            <div class="input-group">
                <label for="followOnReserve">
                    Follow-on Reserve (%)
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Percentage of fund reserved for follow-on investments in existing portfolio companies</span>
                    </span>
                </label>
                <input type="number" id="followOnReserve" value="40" min="0" max="70" step="5">
            </div>
            <div class="input-group checkbox-group">
                <label>
                    <input type="checkbox" id="enableFollowOn" checked>
                    Enable Follow-on Strategy
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Model follow-on investments based on company performance</span>
                    </span>
                </label>
            </div>
            <div class="input-group checkbox-group">
                <label>
                    <input type="checkbox" id="enableRecycling" checked>
                    Enable Capital Recycling
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Reinvest returns from early exits (up to 105% of fund). Later stage deals have higher recycling rates due to shorter time to liquidity.</span>
                    </span>
                </label>
            </div>
            <div class="input-group">
                <label for="managementFee">
                    Management Fee (% annually)
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Annual management fee as % of committed capital (typically 2% for <$500M, 1.5% for $1B+)</span>
                    </span>
                </label>
                <input type="number" id="managementFee" value="2" min="0" max="5" step="0.25">
            </div>
            <div class="input-group">
                <label for="carriedInterest">
                    Carried Interest (%)
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">GP's share of profits above 1x return (typically 20%)</span>
                    </span>
                </label>
                <input type="number" id="carriedInterest" value="20" min="0" max="30" step="5">
            </div>
            <div class="input-group">
                <label for="fundLife">
                    Fund Life (years)
                    <span class="tooltip">ℹ️
                        <span class="tooltiptext">Total fund life for calculating management fees (typically 10 years)</span>
                    </span>
                </label>
                <input type="number" id="fundLife" value="10" min="8" max="15" step="1">
            </div>
            <div class="button-group">
                <button id="optimizeBtn" onclick="runOptimization()">
                    Optimize Portfolio
                </button>
                <button class="secondary-button" onclick="resetResults()">
                    Reset
                </button>
            </div>
            <div class="progress-bar" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="status-message" id="statusMessage"></div>
        </div>

        <div class="card" style="margin-top: 20px;">
            <h3>Check Size Ranges (randomly sampled per deal)</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
                <div style="text-align: center;">
                    <strong>Seed</strong><br>
                    $1M - $5M
                </div>
                <div style="text-align: center;">
                    <strong>Series A</strong><br>
                    $7M - $15M
                </div>
                <div style="text-align: center;">
                    <strong>Series B</strong><br>
                    $10M - $25M
                </div>
                <div style="text-align: center;">
                    <strong>Series C</strong><br>
                    $15M - $40M
                </div>
                <div style="text-align: center;">
                    <strong>Growth</strong><br>
                    $30M - $50M
                </div>
            </div>
        </div>

        <button class="optimize-btn" onclick="runOptimization()">Optimize Portfolio</button>

        <div class="results" id="results">
            <div class="card" style="background: #f0f8ff; border: 1px solid #3498db; margin-bottom: 20px;">
                <p style="margin: 0; color: #2c3e50;" id="feeNote">
                    <strong>📊 Note:</strong> Showing both <strong>Gross TVPI</strong> (before fees) and <strong>Net TVPI</strong> (after fees).
                    Fee structure will be displayed based on your inputs.
                </p>
            </div>
            <div id="topMixesContainer"></div>
        </div>
    </div>

    <script>
        // Default parameters
        const CHECK_SIZES = {
            "Seed": { min: 1_000_000, max: 5_000_000, avg: 3_000_000 },
            "SeriesA": { min: 7_000_000, max: 15_000_000, avg: 11_000_000 },
            "SeriesB": { min: 10_000_000, max: 25_000_000, avg: 17_500_000 },
            "SeriesC": { min: 15_000_000, max: 40_000_000, avg: 27_500_000 },
            "Growth": { min: 30_000_000, max: 50_000_000, avg: 40_000_000 },
        };

        // Stage-specific exit distributions (deal-level multiples)
        // Parameters: [p0, mu, sigma, alpha, xmin, tail_weight]
        // p0: probability of 0x return (total loss)
        // mu, sigma: parameters for the log-normal component (moderate returns)
        // alpha: shape parameter for the Pareto tail (extreme returns)
        // xmin: threshold where Pareto tail begins
        // tail_weight: probability of extreme returns (given not 0x)
        // 
        // Sources: Hustle Fund, AngelList, Correlation Ventures data
        // Seed: 65% fail, heavy tail for unicorns (1-2% exceed 10x)
        // Growth: 5% fail, tighter distribution like public companies
        const STAGE_PARAMS = {
            "Seed":    [0.65, 0.70, 1.00, 2.3, 10.0, 0.15],
            "SeriesA": [0.35, 0.90, 0.80, 2.5, 10.0, 0.10],
            "SeriesB": [0.20, 1.10, 0.60, 2.8, 10.0, 0.06],  // Slightly better tail
            "SeriesC": [0.10, 1.00, 0.60, 2.5, 8.0, 0.08],   // Much better: higher μ, lower alpha, more tail
            "Growth":  [0.05, 0.95, 0.45, 2.8, 7.0, 0.05],   // Better tail outcomes
        };

        const STAGES_ORDERED = ["Seed", "SeriesA", "SeriesB", "SeriesC", "Growth"];

        const MAX_DEALS_PER_STAGE_HEURISTIC = {
            "Seed": 30,
            "SeriesA": 20,
            "SeriesB": 15,
            "SeriesC": 10,
            "Growth": 7,
        };

        const MAX_TOTAL_DEALS_CONSTRAINT = 100;

        // Follow-on investment parameters
        const FOLLOW_ON_PARAMS = {
            probFollowOn: (currentMultiple, stage) => {
                const baseProb = (() => {
                    if (currentMultiple === 0) return 0;
                    if (currentMultiple < 0.5) return 0.05;   // 5% - Very poor performers
                    if (currentMultiple < 1.0) return 0.1;    // 10% - Poor performers  
                    if (currentMultiple < 1.5) return 0.2;    // 20% - Mediocre (avoid!)
                    if (currentMultiple < 2.0) return 0.4;    // 40% - Getting interesting
                    if (currentMultiple < 3.0) return 0.7;    // 70% - Good performers
                    return 0.9;                               // 90% - Clear winners (3x+)
                })();
                
                // Late stage winners get priority - boost by 5%
                if (stage === "SeriesC" || stage === "Growth") {
                    if (currentMultiple >= 2.0) return Math.min(0.95, baseProb + 0.05);
                }
                
                return baseProb;
            },
            // Follow-on size as multiple of initial check
            followOnMultiple: {
                "Seed": 2.0,      // Series A follow-on
                "SeriesA": 1.6,   // Series B follow-on
                "SeriesB": 1.4,   // Series C follow-on
                "SeriesC": 1.2,   // Series D follow-on
                "Growth": 0.8     // Late stage follow-on
            },
            // Map to next stage for follow-on investments
            nextStage: {
                "Seed": "SeriesA",
                "SeriesA": "SeriesB",
                "SeriesB": "SeriesC",
                "SeriesC": "Growth",
                "Growth": "Growth"  // Growth stays at Growth
            }
        };

        // Recycling parameters (probability of recycling within investment period)
        const RECYCLING_PARAMS = {
            // Probability that a deal returns capital for recycling within investment period (5 years)
            // Based on typical time to liquidity by stage
            recyclingProbability: {
                "Seed": 0.05,      // 5% - Very rare for seed to exit in 5 years
                "SeriesA": 0.10,   // 10% - Occasional fast exit
                "SeriesB": 0.20,   // 20% - Some quick M&A exits
                "SeriesC": 0.35,   // 35% - Many exits in 3-5 years  
                "Growth": 0.50     // 50% - Often IPO/exit in 2-4 years
            },
            // Average multiple on recycled deals (usually the better performers that exit early)
            recyclingMultiple: {
                "Seed": 3.0,       // Early exits are usually the winners
                "SeriesA": 2.5,
                "SeriesB": 2.2,
                "SeriesC": 2.0,
                "Growth": 1.8
            },
            maxRecyclingPercent: 105  // Max 105% of fund size can be invested
        };

        // Statistical functions
        function getRandomCheckSize(stage) {
            const range = CHECK_SIZES[stage];
            return range.min + Math.random() * (range.max - range.min);
        }

        function normalCDF(x, mean, stdDev) {
            const z = (x - mean) / stdDev;
            const t = 1 / (1 + 0.2316419 * Math.abs(z));
            const d = 0.3989423 * Math.exp(-z * z / 2);
            const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
            return z > 0 ? 1 - prob : prob;
        }

        function normalPPF(p, mean, stdDev) {
            // Approximation of inverse normal CDF
            if (p <= 0) return -Infinity;
            if (p >= 1) return Infinity;
            
            const a1 = -3.969683028665376e+01;
            const a2 = 2.209460984245205e+02;
            const a3 = -2.759285104469687e+02;
            const a4 = 1.383577518672690e+02;
            const a5 = -3.066479806614716e+01;
            const a6 = 2.506628277459239e+00;
            
            const b1 = -5.447609879822406e+01;
            const b2 = 1.615858368580409e+02;
            const b3 = -1.556989798598866e+02;
            const b4 = 6.680131188771972e+01;
            const b5 = -1.328068155288572e+01;
            
            const c1 = -7.784894002430293e-03;
            const c2 = -3.223964580411365e-01;
            const c3 = -2.400758277161838e+00;
            const c4 = -2.549732539343734e+00;
            const c5 = 4.374664141464968e+00;
            const c6 = 2.938163982698783e+00;
            
            const d1 = 7.784695709041462e-03;
            const d2 = 3.224671290700398e-01;
            const d3 = 2.445134137142996e+00;
            const d4 = 3.754408661907416e+00;
            
            let q, r;
            
            if (p < 0.02425) {
                q = Math.sqrt(-2 * Math.log(p));
                return mean + stdDev * (((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / 
                       ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
            } else if (p < 0.97575) {
                q = p - 0.5;
                r = q * q;
                return mean + stdDev * (((((a1 * r + a2) * r + a3) * r + a4) * r + a5) * r + a6) * q / 
                       (((((b1 * r + b2) * r + b3) * r + b4) * r + b5) * r + 1);
            } else {
                q = Math.sqrt(-2 * Math.log(1 - p));
                return mean + stdDev * -(((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / 
                       ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
            }
        }

        function drawMultiplesVectorized(stage, numDraws) {
            if (numDraws === 0) return [];
            
            const [p0, mu, sigma, alpha, xmin, tailWeight] = STAGE_PARAMS[stage];
            const results = new Array(numDraws).fill(0);
            
            for (let i = 0; i < numDraws; i++) {
                const randP0 = Math.random();
                
                if (randP0 < p0) {
                    results[i] = 0;
                } else {
                    const randTail = Math.random();
                    
                    if (randTail < tailWeight) {
                        // Pareto distribution
                        const u = Math.random();
                        results[i] = xmin * Math.pow(1 - u, -1 / alpha);
                    } else {
                        // Truncated lognormal
                        const logXmin = Math.log(xmin);
                        const FLogXmin = normalCDF(logXmin, mu, sigma);
                        const u = Math.random();
                        const targetCDF = u * FLogXmin;
                        const logMultiple = normalPPF(targetCDF, mu, sigma);
                        results[i] = Math.min(Math.exp(logMultiple), xmin * (1 - 1e-10));
                    }
                }
            }
            
            return results;
        }

        function simulatePortfolio(mix, fundSize, nTrials, enableFollowOn = true, followOnReservePercent = 40, mgmtFeeAnnual = 0.02, carry = 0.20, fundLife = 10, enableRecycling = true) {
            // First, estimate capital requirements using average check sizes
            let estimatedCapital = 0;
            let totalInvestments = 0;
            const investmentPlan = [];
            
            for (const [stage, numDeals] of Object.entries(mix)) {
                if (numDeals > 0) {
                    estimatedCapital += numDeals * CHECK_SIZES[stage].avg;
                    totalInvestments += numDeals;
                    investmentPlan.push({ stage, numDeals });
                }
            }
            
            if (totalInvestments === 0) {
                return {
                    meanTVPI: 0,
                    medianTVPI: 0,
                    stdevTVPI: 0,
                    probGt1x: 0,
                    probGt3x: 0,
                    probGt5x: 0,
                    meanNetTVPI: 0,
                    medianNetTVPI: 0,
                    probNetGt1x: 0,
                    probNetGt2x: 0,
                    probNetGt3x: 0,
                    requiredCapital: 0,
                    numInvestments: 0,
                    distribution: [],
                    netDistribution: [],
                    followOnStats: null,
                    avgUndeployedCapital: fundSize,
                    meanTVPIDeployed: 0,
                    recyclingStats: null
                };
            }
            
            const trialTVPIs = new Array(nTrials);
            const trialNetTVPIs = new Array(nTrials);
            const trialTVPIsDeployed = new Array(nTrials);
            const trialCapitalInvested = new Array(nTrials);
            const trialRecycledCapital = new Array(nTrials);
            const followOnReserve = enableFollowOn ? (fundSize * followOnReservePercent / 100) : 0;
            const initialCapitalAvailable = fundSize - followOnReserve;
            
            // Calculate total management fees over fund life
            const totalMgmtFees = fundSize * mgmtFeeAnnual * fundLife;
            
            // Track follow-on and recycling statistics
            let totalFollowOns = 0;
            let totalFollowOnCapital = 0;
            let totalRecycledDeals = 0;
            let totalRecycledCapital = 0;
            
            for (let trial = 0; trial < nTrials; trial++) {
                let totalValue = 0;
                let trialFollowOnCapital = 0;
                let trialInitialCapital = 0;
                let trialRecycled = 0;
                let remainingFollowOnReserve = followOnReserve;
                
                // Create companies for this trial with random check sizes
                const companies = [];
                for (const plan of investmentPlan) {
                    for (let i = 0; i < plan.numDeals; i++) {
                        const checkSize = getRandomCheckSize(plan.stage);
                        companies.push({
                            stage: plan.stage,
                            checkSize: checkSize,
                            isRecycled: false
                        });
                        trialInitialCapital += checkSize;
                    }
                }
                
                // Simulate recycling - determine which companies exit early and return capital
                if (enableRecycling) {
                    for (const company of companies) {
                        if (Math.random() < RECYCLING_PARAMS.recyclingProbability[company.stage]) {
                            // This company exits early and returns capital
                            const recyclingMultiple = RECYCLING_PARAMS.recyclingMultiple[company.stage];
                            const returnedCapital = company.checkSize * recyclingMultiple;
                            
                            // Add to recycled capital (up to max % of fund)
                            const maxRecyclable = fundSize * RECYCLING_PARAMS.maxRecyclingPercent / 100;
                            const totalDeployedSoFar = trialInitialCapital + trialFollowOnCapital + trialRecycled;
                            
                            if (totalDeployedSoFar < maxRecyclable) {
                                const recycleAmount = Math.min(company.checkSize, maxRecyclable - totalDeployedSoFar);
                                trialRecycled += recycleAmount;
                                totalRecycledDeals++;
                                company.isRecycled = true;
                                
                                // The returned capital contributes to total value
                                totalValue += returnedCapital;
                            }
                        }
                    }
                }
                
                // Simulate each company (including those that weren't recycled)
                for (const company of companies) {
                    if (!company.isRecycled) {
                        const initialMultiple = drawMultiplesVectorized(company.stage, 1)[0];
                        
                        if (enableFollowOn && remainingFollowOnReserve > 0) {
                            // Determine if this company gets follow-on
                            const probOfFollowOn = FOLLOW_ON_PARAMS.probFollowOn(initialMultiple, company.stage);
                            
                            if (Math.random() < probOfFollowOn) {
                                const followOnSize = company.checkSize * FOLLOW_ON_PARAMS.followOnMultiple[company.stage];
                                
                                if (followOnSize <= remainingFollowOnReserve) {
                                    // Make follow-on investment
                                    remainingFollowOnReserve -= followOnSize;
                                    trialFollowOnCapital += followOnSize;
                                    totalFollowOns++;
                                    
                                    // Follow-on investment gets returns from the next stage
                                    const followOnStage = FOLLOW_ON_PARAMS.nextStage[company.stage];
                                    const followOnReturns = drawMultiplesVectorized(followOnStage, 1);
                                    const followOnMultiple = followOnReturns[0];
                                    
                                    // Add follow-on returns (using next stage's distribution)
                                    totalValue += followOnSize * followOnMultiple;
                                }
                            }
                        }
                        
                        // Add initial investment returns
                        totalValue += company.checkSize * initialMultiple;
                    }
                }
                
                totalFollowOnCapital += trialFollowOnCapital;
                totalRecycledCapital += trialRecycled;
                trialCapitalInvested[trial] = trialInitialCapital;
                trialRecycledCapital[trial] = trialRecycled;
                
                // Calculate Gross TVPI on full fund size
                trialTVPIs[trial] = totalValue / fundSize;
                
                // Calculate Net TVPI after fees
                const grossReturn = totalValue;
                const profits = Math.max(0, grossReturn - fundSize);
                const carriedInterest = profits * carry;
                const netReturn = grossReturn - totalMgmtFees - carriedInterest;
                trialNetTVPIs[trial] = netReturn / fundSize;
                
                // Calculate TVPI on deployed capital only
                const deployedCapital = trialInitialCapital + trialFollowOnCapital + trialRecycled;
                trialTVPIsDeployed[trial] = deployedCapital > 0 ? totalValue / deployedCapital : 0;
            }
            
            // Sort for median calculation
            trialTVPIs.sort((a, b) => a - b);
            trialNetTVPIs.sort((a, b) => a - b);
            trialTVPIsDeployed.sort((a, b) => a - b);
            
            const mean = trialTVPIs.reduce((sum, x) => sum + x, 0) / nTrials;
            const median = trialTVPIs[Math.floor(nTrials / 2)];
            const variance = trialTVPIs.reduce((sum, x) => sum + Math.pow(x - mean, 2), 0) / nTrials;
            const stdev = Math.sqrt(variance);
            
            const meanNet = trialNetTVPIs.reduce((sum, x) => sum + x, 0) / nTrials;
            const medianNet = trialNetTVPIs[Math.floor(nTrials / 2)];
            
            const meanDeployed = trialTVPIsDeployed.reduce((sum, x) => sum + x, 0) / nTrials;
            const avgCapitalInvested = trialCapitalInvested.reduce((sum, x) => sum + x, 0) / nTrials;
            const avgRecycled = trialRecycledCapital.reduce((sum, x) => sum + x, 0) / nTrials;
            
            const probGt1x = trialTVPIs.filter(x => x >= 1).length / nTrials;
            const probGt3x = trialTVPIs.filter(x => x >= 3).length / nTrials;
            const probGt5x = trialTVPIs.filter(x => x >= 5).length / nTrials;
            
            const probNetGt1x = trialNetTVPIs.filter(x => x >= 1).length / nTrials;
            const probNetGt2x = trialNetTVPIs.filter(x => x >= 2).length / nTrials;
            const probNetGt3x = trialNetTVPIs.filter(x => x >= 3).length / nTrials;
            
            const totalDeployed = avgCapitalInvested + (totalFollowOnCapital / nTrials) + avgRecycled;
            
            return {
                meanTVPI: mean,
                medianTVPI: median,
                stdevTVPI: stdev,
                probGt1x,
                probGt3x,
                probGt5x,
                meanNetTVPI: meanNet,
                medianNetTVPI: medianNet,
                probNetGt1x,
                probNetGt2x,
                probNetGt3x,
                requiredCapital: avgCapitalInvested,
                numInvestments: totalInvestments,
                distribution: trialTVPIs,
                netDistribution: trialNetTVPIs,
                followOnStats: enableFollowOn ? {
                    avgFollowOns: totalFollowOns / nTrials,
                    avgFollowOnCapital: totalFollowOnCapital / nTrials,
                    followOnReserve: followOnReserve
                } : null,
                recyclingStats: enableRecycling ? {
                    avgRecycledDeals: totalRecycledDeals / nTrials,
                    avgRecycledCapital: avgRecycled,
                    recyclingAsPercentOfFund: (avgRecycled / fundSize) * 100,
                    feeOffsetPercent: (avgRecycled / (fundSize * mgmtFeeAnnual * fundLife)) * 100
                } : null,
                avgUndeployedCapital: Math.max(0, fundSize - totalDeployed),
                meanTVPIDeployed: meanDeployed
            };
        }

        function* generateMixesRecursive(
            stageIdx,
            currentDeals,
            currentCapital,
            currentTotalDeals,
            fundSize,
            maxTotalDeals,
            orderedStages,
            orderedCheckSizes,
            orderedMaxDeals
        ) {
            const numStages = orderedStages.length;
            
            if (stageIdx === numStages) {
                if (currentTotalDeals > 0) {
                    const mix = {};
                    for (let i = 0; i < numStages; i++) {
                        if (currentDeals[i] > 0) {
                            mix[orderedStages[i]] = currentDeals[i];
                        }
                    }
                    if (Object.keys(mix).length > 0) {
                        yield mix;
                    }
                }
                return;
            }
            
            const capHeuristic = orderedMaxDeals[stageIdx];
            const capRemainingDeals = maxTotalDeals - currentTotalDeals;
            const checkSize = orderedCheckSizes[stageIdx];
            const capRemainingCapital = checkSize > 0 
                ? Math.floor((fundSize - currentCapital) / checkSize)
                : Infinity;
            
            const maxN = Math.max(0, Math.min(capHeuristic, capRemainingDeals, capRemainingCapital));
            
            for (let n = 0; n <= maxN; n++) {
                currentDeals[stageIdx] = n;
                yield* generateMixesRecursive(
                    stageIdx + 1,
                    currentDeals,
                    currentCapital + n * checkSize,
                    currentTotalDeals + n,
                    fundSize,
                    maxTotalDeals,
                    orderedStages,
                    orderedCheckSizes,
                    orderedMaxDeals
                );
            }
        }

        let currentOptimization = null;
        let distributionCharts = {}; // Changed to object to track multiple charts

        async function runOptimization() {
            const fundSizeM = parseFloat(document.getElementById('fundSize').value);
            const numTrials = parseInt(document.getElementById('numTrials').value);
            const fundSize = fundSizeM * 1_000_000;
            const enableFollowOn = document.getElementById('enableFollowOn').checked;
            const followOnReservePercent = parseFloat(document.getElementById('followOnReserve').value);
            const mgmtFeeAnnual = parseFloat(document.getElementById('managementFee').value) / 100;
            const carry = parseFloat(document.getElementById('carriedInterest').value) / 100;
            const fundLife = parseInt(document.getElementById('fundLife').value);
            const enableRecycling = document.getElementById('enableRecycling').checked;
            
            // Adjust fund size for initial investments if follow-on is enabled
            const initialCapitalAvailable = enableFollowOn 
                ? fundSize * (100 - followOnReservePercent) / 100 
                : fundSize;
            
            // Disable button during optimization
            const optimizeBtn = document.getElementById('optimizeBtn');
            optimizeBtn.disabled = true;
            
            // Show progress
            document.getElementById('progressBar').classList.add('show');
            document.getElementById('statusMessage').textContent = 'Optimizing portfolio...';
            
            // Clear previous results
            document.getElementById('results').classList.remove('show');
            
            const orderedCheckSizes = STAGES_ORDERED.map(s => CHECK_SIZES[s].avg);
            const orderedMaxDeals = STAGES_ORDERED.map(s => MAX_DEALS_PER_STAGE_HEURISTIC[s]);
            
            // Track top 5 mixes
            const topMixes = [];
            const MAX_TOP_MIXES = 5;
            
            let mixCount = 0;
            
            const startTime = Date.now();
            
            // Create generator - use initial capital available for constraints
            const mixGenerator = generateMixesRecursive(
                0,
                new Array(STAGES_ORDERED.length).fill(0),
                0,
                0,
                initialCapitalAvailable,  // Use adjusted capital for initial investments
                MAX_TOTAL_DEALS_CONSTRAINT,
                STAGES_ORDERED,
                orderedCheckSizes,
                orderedMaxDeals
            );
            
            // Process mixes in batches to allow UI updates
            const processBatch = async () => {
                const batchSize = 100;
                let processed = 0;
                
                for (const mix of mixGenerator) {
                    mixCount++;
                    const stats = simulatePortfolio(mix, fundSize, numTrials, enableFollowOn, followOnReservePercent, mgmtFeeAnnual, carry, fundLife, enableRecycling);
                    
                    // Check if this mix should be in top 5
                    const mixData = { mix: { ...mix }, stats, meanTVPI: stats.meanTVPI };
                    
                    if (topMixes.length < MAX_TOP_MIXES) {
                        topMixes.push(mixData);
                        topMixes.sort((a, b) => b.meanTVPI - a.meanTVPI);
                    } else if (stats.meanTVPI > topMixes[MAX_TOP_MIXES - 1].meanTVPI) {
                        topMixes[MAX_TOP_MIXES - 1] = mixData;
                        topMixes.sort((a, b) => b.meanTVPI - a.meanTVPI);
                    }
                    
                    processed++;
                    if (processed >= batchSize) {
                        // Update progress
                        document.getElementById('statusMessage').textContent = 
                            `Evaluated ${mixCount.toLocaleString()} portfolio mixes...`;
                        
                        // Allow UI to update
                        await new Promise(resolve => setTimeout(resolve, 0));
                        return processBatch();
                    }
                }
                
                // Optimization complete
                const endTime = Date.now();
                const duration = ((endTime - startTime) / 1000).toFixed(1);
                
                document.getElementById('progressBar').classList.remove('show');
                document.getElementById('statusMessage').textContent = 
                    `Optimization complete! Evaluated ${mixCount.toLocaleString()} mixes in ${duration}s`;
                
                // Display results for all top mixes
                displayTopMixes(topMixes, fundSize);
                
                // Re-enable button
                optimizeBtn.disabled = false;
            };
            
            await processBatch();
        }

        function displayTopMixes(topMixes, fundSize) {
            if (!topMixes || topMixes.length === 0) {
                document.getElementById('statusMessage').textContent = 
                    'No feasible portfolio mix found within constraints.';
                return;
            }
            
            // Show results section
            document.getElementById('results').classList.add('show');
            
            // Update fee note with actual parameters
            const mgmtFee = parseFloat(document.getElementById('managementFee').value);
            const carry = parseFloat(document.getElementById('carriedInterest').value);
            const fundLife = parseInt(document.getElementById('fundLife').value);
            
            document.getElementById('feeNote').innerHTML = `
                <strong>📊 Note:</strong> Showing both <strong>Gross TVPI</strong> (before fees) and <strong>Net TVPI</strong> (after fees).
                Fee structure: ${mgmtFee}% annual management fee for ${fundLife} years + ${carry}% carried interest on profits.
            `;
            
            const container = document.getElementById('topMixesContainer');
            container.innerHTML = '';
            
            // Display each of the top mixes
            topMixes.forEach((mixData, index) => {
                const rank = index + 1;
                const mix = mixData.mix;
                const stats = mixData.stats;
                
                // Create card for this mix
                const mixCard = document.createElement('div');
                mixCard.className = 'card';
                mixCard.style.borderLeft = rank === 1 ? '4px solid #3498db' : '4px solid #95a5a6';
                
                // Header with rank
                const header = document.createElement('h2');
                header.innerHTML = `${rank === 1 ? '🏆 ' : ''}${rank}${rank === 1 ? 'st' : rank === 2 ? 'nd' : rank === 3 ? 'rd' : 'th'} Best Portfolio Mix`;
                header.style.marginBottom = '20px';
                mixCard.appendChild(header);
                
                // Two column layout
                const columnsDiv = document.createElement('div');
                columnsDiv.style.display = 'grid';
                columnsDiv.style.gridTemplateColumns = '1fr 1fr';
                columnsDiv.style.gap = '30px';
                columnsDiv.className = 'two-column-grid';
                
                // Left column - Portfolio Mix
                const leftCol = document.createElement('div');
                
                const mixTitle = document.createElement('h3');
                mixTitle.textContent = 'Portfolio Allocation';
                mixTitle.style.marginBottom = '15px';
                mixTitle.style.color = '#2c3e50';
                leftCol.appendChild(mixTitle);
                
                const portfolioMix = document.createElement('div');
                portfolioMix.className = 'portfolio-mix';
                portfolioMix.style.marginBottom = '0';
                
                for (const stage of STAGES_ORDERED) {
                    if (mix[stage] && mix[stage] > 0) {
                        const numDeals = mix[stage];
                        const checkRange = CHECK_SIZES[stage];
                        const avgCheck = checkRange.avg;
                        const stageTotal = numDeals * avgCheck;
                        
                        const mixItem = document.createElement('div');
                        mixItem.className = 'mix-item';
                        mixItem.innerHTML = `
                            <span class="stage-name">${stage}</span>
                            <span>${numDeals} × $${(checkRange.min / 1e6).toFixed(0)}-${(checkRange.max / 1e6).toFixed(0)}M ≈ $${(stageTotal / 1e6).toFixed(0)}M</span>
                        `;
                        portfolioMix.appendChild(mixItem);
                    }
                }
                leftCol.appendChild(portfolioMix);
                
                // Capital summary
                const capitalSummary = document.createElement('div');
                capitalSummary.style.marginTop = '20px';
                
                const cashDrag = fundSize - stats.requiredCapital;
                
                let followOnSummary = '';
                if (stats.followOnStats) {
                    const avgFollowOnPercent = (stats.followOnStats.avgFollowOnCapital / stats.followOnStats.followOnReserve * 100).toFixed(1);
                    followOnSummary = `
                        <p style="margin-top: 10px;">
                            Follow-on Reserve: $${(stats.followOnStats.followOnReserve / 1e6).toFixed(1)}M<br>
                            Avg Follow-on Deployed: $${(stats.followOnStats.avgFollowOnCapital / 1e6).toFixed(1)}M (${avgFollowOnPercent}%)<br>
                            Avg Follow-on Investments: ${stats.followOnStats.avgFollowOns.toFixed(1)}
                        </p>
                    `;
                }
                
                const dryPowderPercent = ((stats.avgUndeployedCapital / fundSize) * 100).toFixed(1);
                const dryPowderSummary = stats.avgUndeployedCapital > 0 ? `
                    <p style="margin-top: 10px; color: #7f8c8d;">
                        Avg Dry Powder: $${(stats.avgUndeployedCapital / 1e6).toFixed(1)}M (${dryPowderPercent}% of fund)<br>
                        <small style="font-style: italic;">Undeployed capital earning 0% return</small>
                    </p>
                ` : '';
                
                const tvpiDrag = stats.meanTVPIDeployed - stats.meanTVPI;
                const tvpiDragSummary = tvpiDrag > 0.01 ? `
                    <p style="margin-top: 10px; color: #e74c3c; font-weight: 600;">
                        Gross TVPI Drag from Dry Powder: -${tvpiDrag.toFixed(2)}x<br>
                        <small>Gross TVPI on Deployed Capital: ${stats.meanTVPIDeployed.toFixed(2)}x vs Fund Gross TVPI: ${stats.meanTVPI.toFixed(2)}x</small>
                    </p>
                ` : '';
                
                capitalSummary.innerHTML = `
                    <p style="font-weight: 600;">
                        Initial Capital: $${(stats.requiredCapital / 1e6).toFixed(1)}M
                        ${cashDrag > 0.01 * fundSize && !stats.followOnStats ? 
                            `<span style="color: #e74c3c;"> (Cash Drag: $${(cashDrag / 1e6).toFixed(1)}M)</span>` : 
                            ''}
                    </p>
                    ${followOnSummary}
                    ${dryPowderSummary}
                    ${tvpiDragSummary}
                `;
                leftCol.appendChild(capitalSummary);
                
                // Right column - Metrics
                const rightCol = document.createElement('div');
                
                const metricsTitle = document.createElement('h3');
                metricsTitle.textContent = 'Performance Metrics';
                metricsTitle.style.marginBottom = '15px';
                metricsTitle.style.color = '#2c3e50';
                rightCol.appendChild(metricsTitle);
                
                const metricsGrid = document.createElement('div');
                metricsGrid.className = 'metrics-grid';
                metricsGrid.style.marginTop = '0';
                
                const metrics = [
                    { label: 'Mean Gross TVPI', value: `${stats.meanTVPI.toFixed(2)}x`, main: true },
                    { label: 'Mean Net TVPI', value: `${stats.meanNetTVPI.toFixed(2)}x`, main: true, net: true },
                    { label: 'Median Gross TVPI', value: `${stats.medianTVPI.toFixed(2)}x` },
                    { label: 'Median Net TVPI', value: `${stats.medianNetTVPI.toFixed(2)}x`, net: true },
                    { label: 'StdDev TVPI', value: `${stats.stdevTVPI.toFixed(2)}x` },
                    { label: 'P(Net TVPI ≥ 1x)', value: `${(stats.probNetGt1x * 100).toFixed(1)}%`, net: true },
                    { label: 'P(Net TVPI ≥ 2x)', value: `${(stats.probNetGt2x * 100).toFixed(1)}%`, net: true },
                    { label: 'P(Net TVPI ≥ 3x)', value: `${(stats.probNetGt3x * 100).toFixed(1)}%`, net: true },
                    { label: 'Total Investments', value: stats.numInvestments },
                    { label: 'Gross TVPI on Deployed', value: `${stats.meanTVPIDeployed.toFixed(2)}x`, highlight: true }
                ];
                
                metrics.forEach(metric => {
                    const card = document.createElement('div');
                    card.className = 'metric-card';
                    if (metric.highlight) {
                        card.style.borderColor = '#e74c3c';
                        card.style.borderWidth = '2px';
                    }
                    if (metric.main && rank === 1 && !metric.net) {
                        card.style.borderColor = '#3498db';
                        card.style.borderWidth = '2px';
                    }
                    if (metric.net) {
                        card.style.backgroundColor = '#fff9e6';
                    }
                    card.innerHTML = `
                        <div class="metric-label">${metric.label}</div>
                        <div class="metric-value" ${metric.highlight ? 'style="color: #e74c3c;"' : metric.main && rank === 1 && !metric.net ? 'style="color: #3498db;"' : metric.net ? 'style="color: #f39c12;"' : ''}>${metric.value}</div>
                    `;
                    metricsGrid.appendChild(card);
                });
                
                rightCol.appendChild(metricsGrid);
                
                columnsDiv.appendChild(leftCol);
                columnsDiv.appendChild(rightCol);
                mixCard.appendChild(columnsDiv);
                
                // Add chart section below the two columns
                const chartSection = document.createElement('div');
                chartSection.style.marginTop = '30px';
                
                const chartTitle = document.createElement('h3');
                chartTitle.textContent = 'Gross Return Distribution';
                chartTitle.style.marginBottom = '15px';
                chartTitle.style.color = '#2c3e50';
                chartSection.appendChild(chartTitle);
                
                const chartContainer = document.createElement('div');
                chartContainer.className = 'chart-container';
                chartContainer.style.height = '300px';
                
                const canvas = document.createElement('canvas');
                canvas.id = `distributionChart${rank}`;
                chartContainer.appendChild(canvas);
                
                chartSection.appendChild(chartContainer);
                mixCard.appendChild(chartSection);
                
                // Add recycling statistics if enabled
                if (stats.recyclingStats) {
                    const recyclingSummary = document.createElement('div');
                    recyclingSummary.style.marginTop = '20px';
                    recyclingSummary.style.padding = '15px';
                    recyclingSummary.style.backgroundColor = '#e8f8f5';
                    recyclingSummary.style.borderRadius = '8px';
                    recyclingSummary.style.border = '1px solid #2ecc71';
                    
                    const totalCapitalInvested = stats.requiredCapital + 
                        (stats.followOnStats ? stats.followOnStats.avgFollowOnCapital : 0) + 
                        stats.recyclingStats.avgRecycledCapital;
                    const totalInvestedPercent = (totalCapitalInvested / fundSize) * 100;
                    
                    recyclingSummary.innerHTML = `
                        <h4 style="color: #27ae60; margin-bottom: 10px;">♻️ Capital Recycling Impact</h4>
                        <p style="margin: 5px 0;">
                            Avg Recycled Deals: ${stats.recyclingStats.avgRecycledDeals.toFixed(1)}<br>
                            Avg Recycled Capital: $${(stats.recyclingStats.avgRecycledCapital / 1e6).toFixed(1)}M (${stats.recyclingStats.recyclingAsPercentOfFund.toFixed(1)}% of fund)<br>
                            <strong style="color: #27ae60;">Total Capital Invested: $${(totalCapitalInvested / 1e6).toFixed(1)}M (${totalInvestedPercent.toFixed(1)}% of fund)</strong><br>
                            <small style="color: #7f8c8d;">Recycling offsets ${stats.recyclingStats.feeOffsetPercent.toFixed(0)}% of management fees</small>
                        </p>
                    `;
                    
                    mixCard.appendChild(recyclingSummary);
                }
                
                container.appendChild(mixCard);
                
                // Create chart for this portfolio
                createDistributionChart(stats.distribution, rank);
            });
        }

        function createDistributionChart(distribution, rank) {
            // Define buckets
            const buckets = [
                { min: 0, max: 0.5, label: '0-0.5x' },
                { min: 0.5, max: 1.0, label: '0.5-1x' },
                { min: 1.0, max: 1.5, label: '1-1.5x' },
                { min: 1.5, max: 2.0, label: '1.5-2x' },
                { min: 2.0, max: 2.5, label: '2-2.5x' },
                { min: 2.5, max: 3.0, label: '2.5-3x' },
                { min: 3.0, max: 4.0, label: '3-4x' },
                { min: 4.0, max: 5.0, label: '4-5x' },
                { min: 5.0, max: 7.0, label: '5-7x' },
                { min: 7.0, max: 10.0, label: '7-10x' },
                { min: 10.0, max: Infinity, label: '10x+' }
            ];

            // Count occurrences in each bucket
            const bucketCounts = buckets.map(() => 0);
            distribution.forEach(value => {
                for (let i = 0; i < buckets.length; i++) {
                    if (value >= buckets[i].min && value < buckets[i].max) {
                        bucketCounts[i]++;
                        break;
                    }
                }
            });

            // Convert to percentages
            const totalCount = distribution.length;
            const bucketPercentages = bucketCounts.map(count => (count / totalCount) * 100);

            // Calculate cumulative distribution
            const cumulativePercentages = [];
            let cumSum = 0;
            for (let i = 0; i < bucketPercentages.length; i++) {
                cumSum += bucketPercentages[i];
                cumulativePercentages.push(cumSum);
            }

            // Destroy existing chart if it exists
            if (distributionCharts[rank]) {
                distributionCharts[rank].destroy();
            }

            // Create new chart
            const ctx = document.getElementById(`distributionChart${rank}`).getContext('2d');
            distributionCharts[rank] = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: buckets.map(b => b.label),
                    datasets: [
                        {
                            label: 'Frequency (%)',
                            data: bucketPercentages,
                            backgroundColor: rank === 1 ? 'rgba(52, 152, 219, 0.7)' : 'rgba(149, 165, 166, 0.7)',
                            borderColor: rank === 1 ? 'rgba(52, 152, 219, 1)' : 'rgba(149, 165, 166, 1)',
                            borderWidth: 1,
                            order: 2
                        },
                        {
                            label: 'Cumulative (%)',
                            data: cumulativePercentages,
                            type: 'line',
                            borderColor: 'rgba(231, 76, 60, 1)',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            borderWidth: 2,
                            pointRadius: 3,
                            pointBackgroundColor: 'rgba(231, 76, 60, 1)',
                            order: 1,
                            yAxisID: 'y-cumulative'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: false
                        },
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                boxWidth: 15,
                                padding: 10
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.parsed.y.toFixed(1);
                                    return `${label}: ${value}%`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Gross Return Multiple'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Frequency (%)'
                            },
                            beginAtZero: true,
                            max: Math.max(...bucketPercentages) * 1.2
                        },
                        'y-cumulative': {
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Cumulative (%)'
                            },
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });
        }

        function resetResults() {
            document.getElementById('results').classList.remove('show');
            document.getElementById('progressBar').classList.remove('show');
            document.getElementById('statusMessage').textContent = '';
            
            // Destroy all charts if they exist
            Object.values(distributionCharts).forEach(chart => {
                if (chart) chart.destroy();
            });
            distributionCharts = {};
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Add enter key support for inputs
            document.querySelectorAll('input').forEach(input => {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        runOptimization();
                    }
                });
            });
        });
    </script>
</body>
</html>