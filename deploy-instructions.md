# Deployment Guide for VC Portfolio Tools

## Quick Start Options

### 1. GitHub Pages (Recommended for Teams)
```bash
# Step 1: Create a new GitHub repository
git init
git add *.html
git commit -m "Initial commit of VC portfolio tools"

# Step 2: Create and push to GitHub
git remote add origin https://github.com/YOUR_USERNAME/vc-portfolio-tools.git
git push -u origin main

# Step 3: Enable GitHub Pages
# Go to Settings > Pages > Source: Deploy from branch (main)
# Your tools will be at: https://YOUR_USERNAME.github.io/vc-portfolio-tools/
```

### 2. Netlify Drop (Fastest Option)
1. Visit https://app.netlify.com/drop
2. Drag your folder containing all HTML files
3. Get instant URL (e.g., https://amazing-tesla-123.netlify.app)
4. Optional: Add custom domain in Netlify settings

### 3. Vercel (Great Developer Experience)
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
cd /path/to/DRYPOWDER
vercel --prod

# Follow prompts, get production URL
```

## Adding Basic Authentication

### For Netlify:
Create `_headers` file:
```
/*
  Basic-Auth: team:SecurePassword123!
```

### For Vercel:
Create `vercel.json`:
```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "WWW-Authenticate",
          "value": "Basic realm=\"Restricted Area\""
        }
      ]
    }
  ]
}
```

## Professional Deployment (AWS)

### S3 + CloudFront Setup:
```bash
# Create S3 bucket
aws s3 mb s3://your-vc-tools-bucket

# Enable static website hosting
aws s3 website s3://your-vc-tools-bucket \
  --index-document index.html

# Upload files
aws s3 sync . s3://your-vc-tools-bucket \
  --exclude ".git/*" \
  --exclude "*.md"

# Create CloudFront distribution for HTTPS and caching
```

## Creating an Index Page

Create `index.html` for easy navigation:
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VC Portfolio Optimization Suite</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 40px;
        }
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .tool-card {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
        }
        .tool-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
        }
        .tool-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .tool-description {
            color: #666;
            font-size: 0.95em;
            line-height: 1.5;
        }
        .new-badge {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75em;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VC Portfolio Optimization Suite</h1>
        <p class="subtitle">Data-driven tools for venture capital portfolio construction and management</p>
        
        <div class="tools-grid">
            <a href="portfolio-optimizer.html" class="tool-card">
                <div class="tool-title">Portfolio Optimizer</div>
                <div class="tool-description">
                    Build optimal multi-stage portfolios. Balance risk/return across Seed through Growth investments.
                </div>
            </a>
            
            <a href="single-stage-optimizer.html" class="tool-card">
                <div class="tool-title">Single-Stage Optimizer <span class="new-badge">NEW</span></div>
                <div class="tool-description">
                    Focus on one investment stage. Optimize portfolio size and follow-on strategy with market-based graduation rates.
                </div>
            </a>
            
            <a href="dry-powder-optimizer.html" class="tool-card">
                <div class="tool-title">Dry Powder Optimizer</div>
                <div class="tool-description">
                    Find the optimal follow-on reserve percentage. Minimize dry powder while achieving return targets.
                </div>
            </a>
            
            <a href="follow-on-strategy-tester.html" class="tool-card">
                <div class="tool-title">Follow-On Strategy Tester</div>
                <div class="tool-description">
                    Compare different follow-on strategies. Test your intuition against mathematical models.
                </div>
            </a>
            
            <a href="portfolio-tracker.html" class="tool-card">
                <div class="tool-title">Portfolio Tracker</div>
                <div class="tool-description">
                    Track active portfolio performance. Monitor multiples, follow-on decisions, and fund metrics in real-time.
                </div>
            </a>
        </div>
        
        <div style="margin-top: 40px; padding-top: 30px; border-top: 1px solid #e0e0e0; color: #7f8c8d; font-size: 0.9em;">
            <p><strong>Note:</strong> All calculations use Monte Carlo simulations with power-law return distributions calibrated to venture capital data. Graduation rates reflect 2022-2024 market conditions.</p>
        </div>
    </div>
</body>
</html>
```

## Security Considerations

1. **No Sensitive Data**: These tools don't store data, but avoid putting real portfolio company names
2. **Access Control**: Use authentication for team-only access
3. **HTTPS**: Always use HTTPS (automatic with most platforms)

## Custom Domain Setup

Most platforms support custom domains:
- Netlify: Free SSL, easy setup
- Vercel: Automatic SSL
- GitHub Pages: Supports custom domains with SSL

Example:
```
tools.yourvcfirm.com → your deployment
```

## Sharing with Limited Access

For temporary sharing:
1. Use Netlify Drop with expiring links
2. Create password-protected ZIP files
3. Use Google Drive with restricted access

## Recommended Approach

For most teams, I'd recommend:
1. GitHub Pages for version control + hosting
2. Add the index.html file above
3. Optional: Add Google Analytics to track usage
4. Share the link with your team

This gives you:
- Free hosting
- Version control
- Easy updates
- Professional URL
- No maintenance 